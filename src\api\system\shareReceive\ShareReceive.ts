import request from '@/utils/request'

// 查询礼簿分享接收列表
export function listShareReceive(data) {
  return request({
    url: '/system/shareReceive/list',
    method: 'post',
    data
  })
}

// 查询礼簿分享接收详细
export function getShareReceive(id) {
  return request({
    url: '/system/shareReceive/' + id,
    method: 'get'
  })
}

// 新增礼簿分享接收
export function addShareReceive(data) {
  return request({
    url: '/system/shareReceive',
    method: 'post',
    data: data
  })
}

// 修改礼簿分享接收
export function updateShareReceive(data) {
  return request({
    url: '/system/shareReceive',
    method: 'put',
    data: data
  })
}

// 删除礼簿分享接收
export function delShareReceive(id) {
  return request({
    url: '/system/shareReceive/' + id,
    method: 'delete'
  })
}
