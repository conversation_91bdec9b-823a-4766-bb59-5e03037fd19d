import { getDicts } from '@/api/system/dict/data'
import useDictStore from '@/store/modules/dict'
import { ref, toRefs } from 'vue'

/**
 * 获取字典数据
 * @param args 字典类型数组
 */
export function useDict(...args) {
  const res = ref({})

  return (() => {
    // 初始化结果对象
    args.forEach((dictType) => {
      res.value[dictType] = []
    })

    // 创建Promise数组
    const promises = args.map((dictType) => {
      const dicts = useDictStore().getDict(dictType)
      if (dicts) {
        res.value[dictType] = dicts
        return Promise.resolve()
      }

      return getDicts(dictType).then((resp) => {
        const dictData = resp.data.map((p) => ({
          label: p.dictLabel,
          value: p.dictValue,
          elTagType: p.listClass,
          elTagClass: p.cssClass,
          type: p.dictType
        }))
        res.value[dictType] = dictData
        useDictStore().setDict(dictType, dictData)
      })
    })

    // 并行执行所有Promise
    Promise.all(promises)

    return toRefs(res.value)
  })()
}
