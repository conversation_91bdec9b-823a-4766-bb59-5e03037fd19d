<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="search"
      @refresh="refresh"
      @search="search"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:oss:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:oss:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:oss:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:oss:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
    </el-row>

    <art-table
      v-loading="loading"
      :data="ossList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="search"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column :label="$t('sysOss.fileName')" :show-overflow-tooltip="true" prop="fileName">
        <template #default="{ row }">
          <el-link type="primary" @click="visibleDetail(row)">{{ row.fileName }}</el-link>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('sysOss.originalName')"
        :show-overflow-tooltip="true"
        prop="originalName"
      />
      <el-table-column
        :label="$t('sysOss.fileSuffix')"
        :show-overflow-tooltip="true"
        prop="fileSuffix"
      />
      <el-table-column :label="$t('sysOss.url')" :show-overflow-tooltip="true" prop="url">
        <template #default="scope">
          <div class="big-flex big-items-center big-gap-x-5">
            <file-preview :file-url="scope.row.url" />
            <el-icon class="big-cursor-pointer" @click="handleCopy(scope.row.url)">
              <CopyDocument />
            </el-icon>
          </div>
        </template>
      </el-table-column>

      <el-table-column :label="$t('sysOss.service')" :show-overflow-tooltip="true" prop="service" />
      <el-table-column :label="$t('table.operation')" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:oss:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            >{{ $t('action.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:oss:remove']"
            icon="Delete"
            link
            type="danger"
            @click="handleDelete(scope.row)"
            >{{ $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改OSS对象存储对话框 -->

    <add
      :id="id"
      v-model:show="open"
      :title="title"
      @refreshList="refreshList"
      @close="handleClose"
    />

    <!-- 详情OSS对象存储对话框 -->
    <detail :id="id" v-model:show="detailShow" :title="title" @close="handleDetailClose" />
  </div>
</template>

<script name="Oss" setup>
  import { listDept } from '@/api/system/dept'
  import { delOss, listOss } from '@/api/system/oss/Oss'
  import { listUser } from '@/api/system/user'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import { ElMessageBox } from 'element-plus'
  import { getCurrentInstance, nextTick, onMounted, onUnmounted, reactive, ref, toRefs } from 'vue'
  import Add from './components/add.vue'
  import Detail from './components/detail.vue'

  const { proxy } = getCurrentInstance()

  const ossList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const id = ref()
  const names = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    fromKey: 'sys_oss',
    fromName: 'OSS对象存储',
    deptList: [],
    userList: [],

    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  function refresh(query) {
    query.pageNum = 1
    search(query)
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }

  function search(query) {
    if (query) {
      data.queryParams = { ...data.queryParams, ...query }
    }
    loading.value = true
    listOss(data.queryParams)
      .then(({ data }) => {
        ossList.value = data.records
        total.value = data.totalRow
        loading.value = false
      })
      .catch((err) => {
        loading.value = false
      })
  }

  // 取消按钮
  function cancel() {
    open.value = false
    reset()
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.ossId)
    names.value = selection.map((item) => item.fileName)

    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  /** 新增按钮操作 */
  function handleAdd() {
    handleClose()
    nextTick(() => {
      id.value = undefined
      open.value = true
      title.value = proxy.formatStr(proxy.$t('info.add'), proxy.$t('menu.oss'))
    })
  }

  const handleClose = () => {
    open.value = false
    id.value = undefined
    title.value = ''
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    id.value = row.ossId || ids.value
    title.value = proxy.formatStr(proxy.$t('action.modify'), proxy.$t('menu.oss'))
    open.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const ossIdKeys = row.ossId || ids.value
    const nameArr = row.fileName || names.value
    proxy.$modal
      .confirm(proxy.$t('confirm.delete', { name: nameArr }))
      .then(function () {
        return delOss(ossIdKeys)
      })
      .then(() => {
        search()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/oss/export',
      {
        ...queryParams.value
      },
      `oss_${new Date().getTime()}.xlsx`
    )
  }

  const refreshList = (pageNum) => {
    if (pageNum) {
      data.queryParams.pageNum = 1
    }
    search()
  }

  const visibleDetail = (row) => {
    nextTick(() => {
      id.value = row.ossId
      title.value = proxy.formatStr(proxy.$t('action.view'), proxy.$t('menu.oss'))
      detailShow.value = true
    })
  }
  /** 处理详情弹窗关闭 */
  const handleDetailClose = () => {
    detailShow.value = false
    id.value = undefined
    title.value = ''
  }

  onMounted(() => {
    document.addEventListener('paste', handlePaste)
  })

  onUnmounted(() => {
    document.removeEventListener('paste', handlePaste)
  })
  const handlePaste = (e) => {
    const clipboardData = e.clipboardData
    let files = clipboardData.files
    if (files.length > 0) {
      proxy.$modal
        .confirm(proxy.$t('need.upload'))
        .then(async () => {
          ElMessageBox.close()
          await proxy.$upload(files)
          search()
          proxy.$modal.msgSuccess(proxy.$t('upload.success'))
        })
        .catch(() => {})
    }
  }
  const handleCopy = (url) => {
    const input = document.createElement('input')
    input.value = url
    document.body.appendChild(input)
    input.select()
    document.execCommand('copy')
    document.body.removeChild(input)
    proxy.$msg.success('复制成功')
  }
</script>
