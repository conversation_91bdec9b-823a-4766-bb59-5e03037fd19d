<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="search"
      @refresh="refresh"
      @search="search"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:activitiModel:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:activitiModel:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:activitiModel:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:activitiModel:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-upload
          ref="upload"
          :action="uploadFileUrl"
          :file-list="fileList"
          :limit="1"
          :on-exceed="handleExceed"
          :on-success="handlerSuccess"
          :show-file-list="false"
          class="upload-demo"
        >
          <el-button
            v-hasPermi="['system:activitiModel:export']"
            icon="Upload"
            plain
            type="warning"
            @click="handleImport"
            >{{ $t('button.import') }}
          </el-button>
        </el-upload>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:activitiModel:export']"
          plain
          type="danger"
          @click="handleI18n"
          >{{ $t('app.internationalization') }}
        </el-button>
      </el-col>
    </el-row>
    <art-table
      v-loading="loading"
      :data="activitiModelList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="search"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column
        :label="$t('activitiModel.title')"
        :show-overflow-tooltip="true"
        prop="title"
      >
        <template #default="{ row }">
          <el-link type="primary" @click="visibleDetail(row)">{{ row.title }}</el-link>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('activitiModel.modelKey')"
        :show-overflow-tooltip="true"
        prop="modelKey"
      />
      <el-table-column
        :label="$t('activitiModel.processId')"
        :show-overflow-tooltip="true"
        prop="processId"
      />
      <el-table-column
        :label="$t('activitiModel.deployStates')"
        :show-overflow-tooltip="true"
        prop="deployStates"
      >
        <template #default="scope">
          <dict-tag :options="deploy_status" :value="scope.row.deployStates" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('activitiModel.lastDeployTime')"
        :show-overflow-tooltip="true"
        prop="lastDeployTime"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastDeployTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.operation')" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:model:design']"
            icon="Edit"
            size="mini"
            type="text"
            @click="handleDesign(scope.row)"
            >{{ $t('online.design') }}
          </el-button>
          <el-button icon="Edit" size="mini" type="text" @click="handleI18n(scope.row)"
            >{{ $t('app.internationalization') }}
          </el-button>
          <el-button
            v-hasPermi="['system:model:design']"
            size="mini"
            type="text"
            @click="handleStart(scope.row)"
            >{{ $t('start.test.flow') }}
          </el-button>
          <el-button
            v-hasPermi="['system:model:edit']"
            size="mini"
            type="text"
            @click="deployment(scope.row)"
            >{{ $t('deployment.release') }}
          </el-button>

          <el-button
            v-hasPermi="['system:model:edit']"
            size="mini"
            type="text"
            @click="exportZip(scope.row)"
            >{{ $t('export.flow.xml') }}
          </el-button>
          <el-button
            v-hasPermi="['system:activitiModel:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            >{{ $t('action.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:activitiModel:remove']"
            icon="Delete"
            link
            type="danger"
            @click="handleDelete(scope.row)"
            >{{ $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改activiti_model对话框 -->

    <add
      :id="id"
      v-model:show="open"
      :title="title"
      @refreshList="refreshList"
      @close="handleClose"
    />

    <!-- 详情activiti_model对话框 -->
    <detail :id="id" v-model:show="detailShow" :title="title" @close="handleDetailClose" />

    <!--    在线设计-->
    <el-dialog
      v-model="design"
      :fullscreen="fullscreen"
      align-center
      custom-class="schedule-task-diaog"
      width="80%"
      @close="dialogClose"
      @open="restart"
    >
      <template #title>
        <div class="avue-crud__dialog__header big-flex big-justify-between">
          <span class="el-dialog__title">
            <span
              style="
                display: inline-block;
                background-color: #3478f5;
                width: 3px;
                height: 20px;
                margin-right: 5px;
                float: left;
                margin-top: 2px;
              "
            ></span>
            {{ formatStr('Activiti', $t('workflow.editor')) }}
          </span>
          <div
            class="big-cursor-pointer big-mr-10"
            @click="fullscreen ? (fullscreen = false) : (fullscreen = true)"
          >
            <el-icon>
              <full-screen />
            </el-icon>
          </div>
        </div>
      </template>
      <acitviti-editor :full="fullscreen" :modelId="modelId" @isUpdate="isUpdate" />
    </el-dialog>
  </div>
</template>

<script name="ActivitiModel" setup>
  import AcitvitiEditor from '@/components/Activiti/AcitvitiEditor.vue'
  import {
    delActivitiModel,
    deploy,
    donwloadXml,
    initModelLang,
    listActivitiModel,
    start
  } from '@/api/system/activitiModel/ActivitiModel'
  import Add from './components/add.vue'
  import Detail from './components/detail.vue'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import { formatStr } from '../../../utils/ruoyi'
  import { message } from '@/utils/resetMessage'

  const uploadFileUrl = ref(import.meta.env.VITE_API_URL + '/uploadZip') // 上传文件服务器地址
  const fileList = ref([])
  const { proxy } = getCurrentInstance()
  const { deploy_status } = proxy.useDict('deploy_status')
  const modelId = ref('')
  const headers = reactive({
    Authorization: 'Bearer '
  })
  let fullscreen = ref(false)
  const activitiModelList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const id = ref()
  const modelIds = ref([])
  const names = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')
  let design = ref(false)
  let isUpdates = ref(false)
  const data = reactive({
    fromKey: 't_activiti_model',
    fromName: 'activiti_model',
    deptList: [],
    userList: [],

    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  function isUpdate() {
    isUpdates.value = true
  }

  /**
   * 导出zip
   * @param row
   */
  function exportZip(row) {
    proxy.$modal
      .confirm(proxy.$t('export.deploy.file'), {
        confirmButtonText: proxy.$t('common.confirm'),
        cancelButtonText: proxy.$t('common.cancel'),
        type: 'info'
      })
      .then(() => {
        donwloadXml({ modelId: row.modelKey }).then((res) => {
          const blob = new Blob([res])
          const link = document.createElement('a') // 创建a标签
          link.download = row.title + '.zip' // a标签添加属性
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          document.body.appendChild(link)
          link.click() // 执行下载
          URL.revokeObjectURL(link.href) // 释放url
        })
      })
  }

  function refresh(query) {
    query.pageNum = 1
    search(query)
  }

  function search(query) {
    if (query) {
      data.queryParams = { ...data.queryParams, ...query }
    }
    loading.value = true
    listActivitiModel(data.queryParams)
      .then(({ data }) => {
        activitiModelList.value = data.records
        total.value = data.totalRow
        loading.value = false
      })
      .catch((err) => {
        loading.value = false
      })
  }

  // 取消按钮
  function cancel() {
    open.value = false
    reset()
  }

  /**
   * 部署流程
   */
  function deployment(row) {
    let key = row.modelKey
    proxy.$modal
      .confirm(proxy.$t('deploy.confirm'), {
        confirmButtonText: proxy.$t('common.confirm'),
        cancelButtonText: proxy.$t('common.cancel'),
        type: 'warning'
      })
      .then(() => {
        deploy({ modelId: key }).then((res) => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess(proxy.$t('deploy.success'))
            search()
          } else {
            proxy.$modal.msgError(res.msg)
          }
        })
      })
  }

  /*流程dialog恢复小屏*/
  function restart() {
    fullscreen.value = false
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id)
    names.value = selection.map((item) => item.title)
    modelIds.value = selection.map((item) => item.modelKey)
    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  /** 新增按钮操作 */
  function handleAdd() {
    handleClose()
    nextTick(() => {
      id.value = undefined
      open.value = true
      title.value = proxy.formatStr(proxy.$t('info.add'), proxy.$t('menu.activitiModel'))
    })
  }

  const handleClose = () => {
    open.value = false
    id.value = undefined
    title.value = ''
  }

  /**
   * 在线设置
   */
  function handleDesign(row) {
    modelId.value = row.modelKey
    design.value = true
  }

  function handleStart(row) {
    start({ processDefinitionKey: row.processId, modelId: row.modelKey }).then((res) => {
      proxy.$modal.msgSuccess(proxy.$t('operation.success'))
      search()
    })
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    id.value = row.id || ids.value
    title.value = proxy.$t('action.modify') + ' ' + proxy.$t('menu.activitiModel')
    open.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const idKeys = row.id || ids.value
    const nameArr = row.title || names.value
    proxy.$modal
      .confirm(proxy.$t('confirm.delete', { name: nameArr }))
      .then(function () {
        return delActivitiModel(idKeys)
      })
      .then(() => {
        search()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  function dialogClose() {
    if (isUpdates.value) {
      search()
    }
    design.value = false
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/activitiModel/export',
      {
        ...queryParams.value
      },
      `activitiModel_${new Date().getTime()}.xlsx`
    )
  }

  const refreshList = (pageNum) => {
    if (pageNum) {
      data.queryParams.pageNum = 1
    }
    search()
  }
  const handlerSuccess = ({ code, msg }) => {
    fileList.value = fileList.value.slice(-1) // 保持最后一个文件
    if (code !== 200) {
      message({ message: msg, type: 'error' })
      return
    }
    search()
  }

  const visibleDetail = (row) => {
    handleDetailClose()
    nextTick(() => {
      id.value = row.id
      title.value = proxy.$t('action.view') + ' ' + proxy.$t('menu.activitiModel')
      detailShow.value = true
    })
  }
  /** 处理详情弹窗关闭 */
  const handleDetailClose = () => {
    detailShow.value = false
    id.value = undefined
    title.value = ''
  }
  const handleI18n = (row) => {
    proxy.$modal
      .confirm(proxy.$t('confirm.initI18n'), {
        confirmButtonText: proxy.$t('common.confirm'),
        cancelButtonText: proxy.$t('common.cancel'),
        type: 'info'
      })
      .then(() => {
        const modelKeys = row.modelKey || modelIds.value
        initModelLang(modelKeys).then((res) => {
          message({ message: proxy.$t('operation.success'), type: 'success' })
        })
      })
  }
</script>
<style scoped></style>
