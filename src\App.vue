<template>
  <ElConfigProvider size="default" :locale="locales[language]" :z-index="3000">
    <RouterView></RouterView>
  </ElConfigProvider>
</template>

<script setup lang="ts">
  import { useUserStore } from './store/modules/user'
  import zh from 'element-plus/es/locale/lang/zh-cn'
  import en from 'element-plus/es/locale/lang/en'
  import { systemUpgrade } from './utils/sys'

  import { setThemeTransitionClass } from './utils/theme/animation'
  import { checkStorageCompatibility } from './utils/storage'

  const userStore = useUserStore()
  const { language } = storeToRefs(userStore)

  const locales = {
    zh: zh,
    en: en
  }

  onBeforeMount(() => {
    setThemeTransitionClass(true)
  })

  onMounted(() => {
    // 检查存储兼容性
    checkStorageCompatibility()
    // 提升暗黑主题下页面刷新视觉体验
    setThemeTransitionClass(false)
    // 系统升级
    systemUpgrade()
  })
</script>

<style lang="scss">
  /* 在 App.vue 中添加全局样式，不使用 scoped */
  html,
  body {
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    touch-action: none !important;
    overscroll-behavior: none !important;
  }

  /* 主要内容区域可以滚动 */
  .container {
    width: 100%;
    height: 100%;
    overflow-y: scroll !important;
    -webkit-overflow-scrolling: touch;
  }

  /* 确保表格等可滚动元素正常工作 */
  .el-table__body-wrapper,
  .scrollable-content {
    overflow-y: auto !important;
  }
</style>
