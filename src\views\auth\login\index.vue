<template>
  <div class="login">
    <div class="login-form page-content" style="position: relative">
      <img
        v-show="loginType === 'qrcode'"
        src="@/assets/logo/login_bg_2.png"
        style="position: absolute; right: 2px; top: 2px; margin: 0"
        @click="toggleType('compute')"
      />
      <img
        v-show="loginType === 'compute'"
        src="@/assets/logo/login_bg_3.png"
        style="position: absolute; right: 2px; top: 2px; margin: 0"
        @click="toggleType('qrcode')"
      />

      <div v-show="loginType === 'qrcode'">
        <el-form ref="loginRef" :model="loginForm" :rules="loginRules">
          <h3 class="title" style="padding-top: 25px">{{
            $t('system.management.background.ruoyi')
          }}</h3>

          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              auto-complete="off"
              placeholder="账号"
              size="large"
              type="text"
            >
              <template #prefix><i :class="`iconfont-sys iconsys-user`"></i></template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              auto-complete="off"
              placeholder="密码"
              size="large"
              type="password"
              @keyup.enter="handleLogin"
            >
              <template #prefix>
                <i :class="`iconfont-sys iconsys-mima`"></i>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item v-if="captchaEnabled" prop="code">
            <el-input
              v-model="loginForm.code"
              :placeholder="$t('code.verification')"
              auto-complete="off"
              size="large"
              style="width: 63%"
              @keyup.enter="handleLogin"
            >
              <template #prefix> <i :class="`iconfont-sys iconsys-yanzhengma1`"></i></template>
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" class="login-code-img" @click="getCode" />
            </div>
          </el-form-item>
          <div class="big-flex big-items-center big-justify-between">
            <el-checkbox v-model="loginForm.rememberMe">{{ $t('password.remember') }}</el-checkbox>
            <router-link :to="'/register'" class="link-type" style="font-size: 14px; float: right"
              >{{ $t('register.now') }}
            </router-link>
          </div>

          <el-form-item style="width: 100%">
            <el-button
              :loading="loading"
              size="large"
              style="width: 100%"
              type="primary"
              @click.prevent="handleLogin"
            >
              <span v-if="!loading">{{ $t('login') }}</span>
              <span v-else>{{ $t('login.loading') }}...</span>
            </el-button>
            <!--     v-if="register"     -->
          </el-form-item>
        </el-form>
        <!--  第三方应用登录 -->
        <div class="">
          <div class="big-flex big-flex-row">
            <div class="oauth-login-item" @click="doSocialLogin('gitee')">
              <img src="@/assets/source/gitee.png" style="height: 30px" />
            </div>
            <div class="oauth-login-item" style="margin-left: 10px" @click="doSocialLogin('qq')">
              <img src="@/assets/source/qq.png" style="height: 30px" />
            </div>
            <div
              class="oauth-login-item"
              style="margin-left: 10px"
              @click="doSocialLogin('dingtalk')"
            >
              <img src="@/assets/source/dingTalk.png" style="height: 30px" />
            </div>
          </div>
        </div>
      </div>

      <div v-show="loginType === 'compute'" class="big-pb-10">
        <h3 class="title" style="padding-top: 25px"></h3>

        <div class="code-bgc">
          <iframe
            :src="enterpriseSrc"
            frameborder="0"
            height="336px"
            scrolling="no"
            width="240px"
          />
        </div>
        <!--              <h4 class="mt6" style="color: #707070;">使用微信小程序扫码登录</h4>-->
      </div>
    </div>

    <el-dialog
      v-model="totpDialog"
      :close-on-click-modal="false"
      :title="$t('code.verification.totp')"
      align-center
      width="500"
    >
      <el-form ref="totpRef" :model="loginForm" :rules="loginRules">
        <el-form-item :label="$t('code.verification')" prop="totpCode">
          <el-input v-model="loginForm.totpCode" maxlength="6" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="totpDialog = false">{{ $t('action.cancel') }}</el-button>
          <el-button :loading="totpLoading" type="primary" @click="submitLogin"
            >{{ $t('common.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2018-2024 ruoyi.vip All Rights Reserved.</span>
      <a href="https://beian.miit.gov.cn">湘ICP备19019594号-1</a>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import SvgIcon from '@/components/SvgIcon/index.vue'
  import { getCodeImg, isEnableTotp, loginUser } from '@/api/loginApi'
  import { authBinding } from '@/api/auth'
  import { useUserStore } from '@/store/modules/user'
  import { HOME_PAGE } from '@/router'
  import { useRoute, useRouter } from 'vue-router'
  import { ElLoading } from 'element-plus'

  const userStore = useUserStore()

  const route = useRoute()
  const router = useRouter()
  const { proxy } = getCurrentInstance()
  const { params, query } = route

  const loginForm = ref({
    username: '',
    password: '',
    rememberMe: false,
    code: '',
    uuid: ''
  })

  const loginRules = {
    username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
    password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
    totpCode: [{ required: true, trigger: 'blur', message: '请输入TOTP验证码' }],
    code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
  }

  const codeUrl = ref('')
  const totpDialog = ref(false)
  let loginType = ref('qrcode')
  let enterpriseSrc = ref('')
  const loading = ref(false)
  const totpLoading = ref(false)
  // 验证码开关
  const captchaEnabled = ref(true)
  // 注册开关
  const register = ref(false)
  const redirect = ref(undefined)

  watch(
    route,
    (newRoute) => {
      redirect.value = newRoute.query && newRoute.query.redirect
    },
    { immediate: true }
  )

  if (query['errorType']) {
    const errorType = query['errorType']
    if (errorType === 'unregistered') {
      proxy.$modal.msgError('您没有绑定注册用户，请先注册后在个人中心绑定第三方授权信息！')
    } else if (errorType === 'noUser') {
      proxy.$modal.msgError('用户不存在！')
    } else if (errorType === 'delUser') {
      proxy.$modal.msgError('对不起，您的账号已被删除')
    } else if (errorType === 'disableUser') {
      proxy.$modal.msgError('对不起，您的账号已停用')
    }
  }

  function handleLogin() {
    proxy.$refs.loginRef.validate(async (valid) => {
      if (valid) {
        loading.value = true
        isEnableTotp(loginForm.value.username, loginForm.value.password)
          .then((res) => {
            if (res.data) {
              totpDialog.value = true
              loginForm.value.totpCode = ''
              loading.value = false
            } else {
              login()
            }
          })
          .catch(() => {
            loading.value = false
          })
        return
      }
      loading.value = false
    })
  }

  const login = () => {
    // 调用action的登录方法
    loginUser(loginForm.value)
      .then(({ token }) => {
        userStore.setToken(token)
        redirect.value = redirect.value === undefined ? HOME_PAGE : redirect.value
        router.push(redirect.value)
        totpLoading.value = false
      })
      .catch(() => {
        loading.value = false
        totpLoading.value = false
        // 重新获取验证码
        if (captchaEnabled.value && totpDialog.value === false) {
          getCode()
        }
      })
  }

  function getCode() {
    getCodeImg().then((res) => {
      captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
      if (captchaEnabled.value) {
        codeUrl.value = 'data:image/gif;base64,' + res.img
        loginForm.value.uuid = res.uuid
      }
    })
  }

  if (query.token) {
    localStorage.setItem('username', query.username)
    const loading = ElLoading.service({
      lock: true,
      text: '正在验证第三方回调数据，请稍候',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    setTimeout(() => {
      loading.close()
      router.push({ path: redirect.value || '/' })
    }, 1000)
  }

  function toggleType(type) {
    loginType.value = type
    if (type === 'compute') {
      authBinding('wechatEnterprise').then((res) => {
        enterpriseSrc.value = res.msg
      })
      return
    }
    getCode()
  }

  const doSocialLogin = (source: string) => {
    authBinding(source).then((res) => {
      top.location.href = res.msg
    })
  }
  const submitLogin = () => {
    totpLoading.value = true
    proxy.$refs.totpRef.validate((valid) => {
      if (valid) {
        login()
      }
    })
  }
  getCode()
</script>

<style lang="scss" scoped>
  .login {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-image: url('@/assets/img/login-background.jpg');
    background-size: cover;
  }

  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #707070;
  }

  .login-form {
    border-radius: 8px;
    background: #ffffff;
    width: 400px;
    padding: 0px 25px 5px 25px;

    .el-input {
      height: 40px;

      input {
        height: 40px;
      }
    }

    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 0px;
    }
  }

  .login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
  }

  .login-code {
    width: 33%;
    height: 40px;
    float: right;

    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }

  .code-bgc {
    width: 260px;
    height: 260px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url(data:image/png;base64,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);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50%;
    margin: 0 auto;
  }

  .el-login-footer {
    height: 50px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }

  .login-code-img {
    height: 40px;
    padding-left: 12px;
  }

  .oauth-login-item {
    cursor: pointer;
  }
</style>
