<template></template>

<script lang="ts" setup>
  import { ElLoading } from 'element-plus'
  import { useUserStore } from '@/store/modules/user'
  import { useRoute, useRouter } from 'vue-router'
  import { HOME_PAGE } from '@/router'

  const userStore = useUserStore()

  const route = useRoute()
  const router = useRouter()
  const { params, query } = route

  const openFullScreen = () => {
    const loading = ElLoading.service({
      lock: true,
      text: '正在验证第三方回调数据，请稍候',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    if (query.token) {
      userStore.setToken(query.token)

      setTimeout(() => {
        loading.close()
        router.push(HOME_PAGE)
      }, 1000)
    }
  }
  openFullScreen()
</script>
