<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="search"
      @refresh="refresh"
      @search="search"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:productCategories:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:productCategories:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:productCategories:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:productCategories:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
    </el-row>

    <art-table
      v-loading="loading"
      :data="productCategoriesList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="search"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <!--${comment}-->
      <!--分类名称-->
      <el-table-column
        :label="$t('productCategories.name')"
        :show-overflow-tooltip="true"
        prop="name"
      >
        <template #default="{ row }">
          <el-link type="primary" @click="visibleDetail(row)">{{ row.name }}</el-link>
        </template>
      </el-table-column>
      <!--分类图标-->
      <el-table-column
        :label="$t('productCategories.icon')"
        :show-overflow-tooltip="true"
        prop="icon"
      >
        <template #default="scope">
          <image-preview :height="50" :src="scope.row.icon" :width="50" />
        </template>
      </el-table-column>
      <!--排序-->
      <el-table-column
        :label="$t('productCategories.sortOrder')"
        :show-overflow-tooltip="true"
        prop="sortOrder"
      />
      <!--状态:0=禁用,1=启用-->
      <el-table-column
        :label="$t('productCategories.status')"
        :show-overflow-tooltip="true"
        prop="status"
      />
      <el-table-column :label="$t('table.operation')" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:productCategories:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            >{{ $t('action.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:productCategories:remove']"
            icon="Delete"
            link
            type="danger"
            @click="handleDelete(scope.row)"
            >{{ $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改产品分类对话框 -->

    <add
      :id="id"
      v-model:show="open"
      :title="title"
      @refreshList="refreshList"
      @close="handleClose"
    />

    <!-- 详情产品分类对话框 -->
    <detail :id="id" v-model:show="detailShow" :title="title" @close="handleDetailClose" />
  </div>
</template>

<script name="ProductCategories" setup>
  import {
    delProductCategories,
    listProductCategories
  } from '@/api/system/productCategories/ProductCategories'
  import Add from './components/add.vue'
  import Detail from './components/detail.vue'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'

  const { proxy } = getCurrentInstance()

  const productCategoriesList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const id = ref()
  const names = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    fromKey: 't_product_categories',
    fromName: '产品分类',
    deptList: [],
    userList: [],

    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  function refresh(query) {
    query.pageNum = 1
    search(query)
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }

  function search(query) {
    if (query) {
      data.queryParams = { ...data.queryParams, ...query }
    }
    loading.value = true
    listProductCategories(data.queryParams)
      .then(({ data }) => {
        productCategoriesList.value = data.records
        total.value = data.totalRow
        loading.value = false
      })
      .catch((err) => {
        loading.value = false
      })
  }

  // 取消按钮
  function cancel() {
    open.value = false
    reset()
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id)
    names.value = selection.map((item) => item.name)

    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  /** 新增按钮操作 */
  function handleAdd() {
    handleClose()
    nextTick(() => {
      id.value = undefined
      open.value = true
      title.value = proxy.formatStr(proxy.$t('info.add'), proxy.$t('menu.productCategories'))
    })
  }

  const handleClose = () => {
    open.value = false
    id.value = undefined
    title.value = ''
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    id.value = row.id || ids.value
    title.value = proxy.formatStr(
      proxy.$t('action.modify') + ' ' + proxy.$t('menu.productCategories')
    )
    open.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const idKeys = row.id || ids.value
    const nameArr = row.name || names.value
    proxy.$modal
      .confirm(proxy.$t('confirm.delete', { name: nameArr }))
      .then(function () {
        return delProductCategories(idKeys)
      })
      .then(() => {
        search()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/productCategories/export',
      {
        ...queryParams.value
      },
      `productCategories_${new Date().getTime()}.xlsx`
    )
  }

  const refreshList = (pageNum) => {
    if (pageNum) {
      data.queryParams.pageNum = 1
    }
    search()
  }

  const visibleDetail = (row) => {
    nextTick(() => {
      id.value = row.id
      title.value = proxy.formatStr(proxy.$t('action.view'), proxy.$t('menu.productCategories'))
      detailShow.value = true
    })
  }
  /** 处理详情弹窗关闭 */
  const handleDetailClose = () => {
    detailShow.value = false
    id.value = undefined
    title.value = ''
  }
</script>
