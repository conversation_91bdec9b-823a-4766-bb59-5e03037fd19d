<template>
  <el-form ref="pwdRef" :model="user" :rules="rules">
    <el-form-item :label="$t('password.old')" prop="oldPassword">
      <el-input
        v-model="user.oldPassword"
        :placeholder="$t('password.old.input')"
        show-password
        type="password"
      />
    </el-form-item>
    <el-form-item :label="$t('password.new')" prop="newPassword">
      <el-input
        v-model="user.newPassword"
        :placeholder="$t('password.new.input')"
        show-password
        type="password"
      />
    </el-form-item>
    <el-form-item :label="$t('password.confirm')" prop="confirmPassword">
      <el-input
        v-model="user.confirmPassword"
        :placeholder="$t('password.new.confirm')"
        show-password
        type="password"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submit">{{ $t('action.save') }}</el-button>
      <el-button type="danger" @click="close">{{ $t('action.close') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
  import { getCurrentInstance } from 'vue'
  import { updateUserPwd } from '@/api/system/user'
  import { useWorktabStore } from '@/store/modules/worktab'

  const { proxy } = getCurrentInstance()

  const store = useWorktabStore()

  const user = reactive({
    oldPassword: undefined,
    newPassword: undefined,
    confirmPassword: undefined
  })

  const equalToPassword = (rule, value, callback) => {
    if (user.newPassword !== value) {
      callback(new Error(proxy.$t('passwords.match.not')))
    } else {
      callback()
    }
  }

  const rules = ref({
    oldPassword: [{ required: true, message: proxy.$t('password.old.empty'), trigger: 'blur' }],
    newPassword: [
      { required: true, message: proxy.$t('password.new.empty'), trigger: 'blur' },
      {
        min: 6,
        max: 20,
        message: proxy.$t('length.characters.between'),
        trigger: 'blur'
      },
      { pattern: /^[^<>"'|\\]+$/, message: proxy.$t('characters.illegal'), trigger: 'blur' }
    ],
    confirmPassword: [
      { required: true, message: proxy.$t('password.confirmation.empty'), trigger: 'blur' },
      {
        required: true,
        validator: equalToPassword,
        trigger: 'blur'
      }
    ]
  })

  /** 提交按钮 */
  function submit() {
    proxy.$refs.pwdRef.validate((valid) => {
      if (valid) {
        updateUserPwd(user.oldPassword, user.newPassword).then((response) => {
          proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        })
      }
    })
  }

  /** 关闭按钮 */
  function close() {
    console.log(proxy.$t('action.come.in'))
    store.closePage()
    console.log(proxy.$t('action.come.in.1'))
  }
</script>
