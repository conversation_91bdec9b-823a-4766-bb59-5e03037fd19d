{"HEADER.BRAND": "Activiti Editor", "HEADER.BRAND_TAGLINE": "powered by Alfresco", "PAGE.HEADER": "Orchestration Details", "ACTION.OK": "Ok", "ACTION.SAVE": "Save", "ACTION.SAVE-AND-CLOSE": "Save and close editor", "ACTION.SEND": "Send", "ACTION.CANCEL": "Cancel", "ACTION.SELECT": "Select", "ACTION.ADD": "Add", "ACTION.REMOVE": "Remove", "ACTION.MOVE.UP": "Move entry up", "ACTION.MOVE.DOWN": "Move entry down", "MAIN_NAVIGATION_ORCHESTRATIONS": "Orchestrations", "MAIN_NAVIGATION_DISPATCH_RULES": "Dispatch Rules", "MAIN_NAVIGATION_ASSET_GROUPS": "Assert Groups", "MAIN_NAVIGATION_SOLUTIONS": "Solutions", "TOOLBAR.ACTION.CLOSE": "Close the editor and go back to the overview page", "TOOLBAR.ACTION.SAVE": "Save the model", "TOOLBAR.ACTION.VALIDATE": "Validate the model", "TOOLBAR.ACTION.CUT": "Cut (select one or more elements in your business process)", "TOOLBAR.ACTION.COPY": "Copy (select one or more elements in your business process)", "TOOLBAR.ACTION.PASTE": "Paste", "TOOLBAR.ACTION.DELETE": "Delete the selected element", "TOOLBAR.ACTION.UNDO": "Undo", "TOOLBAR.ACTION.REDO": "Redo", "TOOLBAR.ACTION.ZOOMIN": "Zoom in", "TOOLBAR.ACTION.ZOOMOUT": "Zoom out", "TOOLBAR.ACTION.ZOOMACTUAL": "Zoom to actual size", "TOOLBAR.ACTION.ZOOMFIT": "Zoom to fit", "TOOLBAR.ACTION.MOVE": "Move", "TOOLBAR.ACTION.IMPORT": "Import", "TOOLBAR.ACTION.EXPORT": "Export", "TOOLBAR.ACTION.BENDPOINT.ADD": "Add bend-point to the selected sequence flow", "TOOLBAR.ACTION.BENDPOINT.REMOVE": "Remove bend-point from the selected sequence flow", "TOOLBAR.ACTION.ALIGNHORIZONTAL": "Align model horizontal", "TOOLBAR.ACTION.ALIGNVERTICAL": "Align model vertical", "TOOLBAR.ACTION.SAMESIZE": "Same size", "TOOLBAR.ACTION.HELP": "Start the guided tour", "TOOLBAR.ACTION.FEEDBACK": "Provide feedback", "KICKSTART.PROCESS_TOOLBAR.ACTION.SAVE": "Save the model", "KICKSTART.PROCESS_TOOLBAR.ACTION.VALIDATE": "Validate the model", "KICKSTART.PROCESS_TOOLBAR.ACTION.HELP": "Start the guided tour", "KICKSTART.PROCESS_TOOLBAR.ACTION.FEEDBACK": "Provide feedback", "FORM_TOOLBAR.ACTION.SAVE": "Save the model", "FORM_TOOLBAR.ACTION.VALIDATE": "Validate the model", "FORM_TOOLBAR.ACTION.HELP": "Start the guided tour", "FORM_TOOLBAR.ACTION.FEEDBACK": "Provide feedback", "APP_DEFINITION_TOOLBAR.ACTION.SAVE": "Save the app definition", "APP_DEFINITION_TOOLBAR.ACTION.VALIDATE": "Validate the app definition", "APP_DEFINITION_TOOLBAR.ACTION.HELP": "Start the guided tour", "APP_DEFINITION_TOOLBAR.ACTION.FEEDBACK": "Provide feedback", "BUTTON.ACTION.DELETE.TOOLTIP": "Delete the element from the model", "BUTTON.ACTION.MORPH.TOOLTIP": "Change the element type", "ELEMENT.AUTHOR": "Author", "ELEMENT.DATE_CREATED": "Date created", "ELEMENT.SELECTED_EMPTY_TITLE": "(No name)", "PROPERTY.REMOVED": "removed", "PROPERTY.EMPTY": "No value", "PROPERTY.PROPERTY.EDIT.TITLE": "Change value for \"{{title}}\"", "PROPERTY.FEEDBACK.TITLE": "Please fill-in your feedback", "PROPERTY.ASSIGNMENT.TITLE": "Assignment", "PROPERTY.ASSIGNMENT.TYPE": "Type", "PROPERTY.ASSIGNMENT.TYPE.IDENTITYSTORE": "Identity store", "PROPERTY.ASSIGNMENT.TYPE.STATIC": "Static values", "PROPERTY.ASSIGNMENT.ASSIGNEE": "Assignee", "PROPERTY.ASSIGNMENT.MATCHING": "Use &uparrow; and &downarrow; to select and press Enter to confirm or use the mouse", "PROPERTY.ASSIGNMENT.ASSIGNEE_PLACEHOLDER": "Enter an assignee", "PROPERTY.ASSIGNMENT.EMPTY": "No assignment selected", "PROPERTY.ASSIGNMENT.ASSIGNEE_DISPLAY": "Assignee {{assignee}}", "PROPERTY.ASSIGNMENT.CANDIDATE_USERS_DISPLAY": "{{length}} Candidate users", "PROPERTY.ASSIGNMENT.CANDIDATE_USERS": "Candidate users", "PROPERTY.ASSIGNMENT.CANDIDATE_GROUPS_DISPLAY": "{{length}} Candidate groups", "PROPERTY.ASSIGNMENT.CANDIDATE_GROUPS": "Candidate groups", "PROPERTY.ASSIGNMENT.USER_IDM_DISPLAY": "User {{firstName}} {{lastName}}", "PROPERTY.ASSIGNMENT.USER_IDM_EMAIL_DISPLAY": "User {{email}}", "PROPERTY.ASSIGNMENT.IDM_EMPTY": "Process initiator", "PROPERTY.ASSIGNMENT.IDM.TYPE": "Assignment", "PROPERTY.ASSIGNMENT.IDM.NO_CANDIDATE_USERS": "No candidate users selected...", "PROPERTY.ASSIGNMENT.IDM.NO_CANDIDATE_GROUPS": "No candidate groups selected...", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.INITIATOR": "Assigned to process initiator", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.USER": "Assigned to single user", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.USERS": "Candidate users", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.GROUPS": "Candidate groups", "PROPERTY.ASSIGNMENT.EMAIL.HELP": "Type an email address and press Enter to continue", "PROPERTY.EXECUTIONLISTENERS.DISPLAY": "{{length}} execution listeners", "PROPERTY.EXECUTIONLISTENERS.EMPTY": "No execution listeners configured", "PROPERTY.EXECUTIONLISTENERS.EVENT": "Event", "PROPERTY.EXECUTIONLISTENERS.CLASS": "Class", "PROPERTY.EXECUTIONLISTENERS.CLASS.PLACEHOLDER": "Enter a classname", "PROPERTY.EXECUTIONLISTENERS.EXPRESSION": "Expression", "PROPERTY.EXECUTIONLISTENERS.EXPRESSION.PLACEHOLDER": "Enter an expression", "PROPERTY.EXECUTIONLISTENERS.DELEGATEEXPRESSION": "Delegate expression", "PROPERTY.EXECUTIONLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER": "Enter a delegate expression", "PROPERTY.EXECUTIONLISTENERS.UNSELECTED": "No execution listener selected", "PROPERTY.EXECUTIONLISTENERS.FIELDS.NAME": "Name", "PROPERTY.EXECUTIONLISTENERS.FIELDS.NAME.PLACEHOLDER": "Enter a name", "PROPERTY.EXECUTIONLISTENERS.FIELDS.EXPRESSION": "Expression", "PROPERTY.EXECUTIONLISTENERS.FIELDS.EXPRESSION.PLACEHOLDER": "Enter an expression", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRINGVALUE": "String value", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRINGVALUE.PLACEHOLDER": "Enter a string value", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRING": "String", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRING.PLACEHOLDER": "Enter a string", "PROPERTY.EXECUTIONLISTENERS.FIELDS.IMPLEMENTATION": "Implementation", "PROPERTY.EXECUTIONLISTENERS.FIELDS.EMPTY": "No Field selected", "PROPERTY.FIELDS": "{{length}} fields", "PROPERTY.FIELDS.EMPTY": "No fields selected", "PROPERTY.FIELDS.NAME": "Name", "PROPERTY.FIELDS.NAME.PLACEHOLDER": "Enter a name", "PROPERTY.FIELDS.EXPRESSION": "Expression", "PROPERTY.FIELDS.EXPRESSION.PLACEHOLDER": "Enter an expression", "PROPERTY.FIELDS.STRINGVALUE": "String value", "PROPERTY.FIELDS.STRINGVALUE.PLACEHOLDER": "Enter a string value", "PROPERTY.FIELDS.STRING": "String", "PROPERTY.FIELDS.STRING.PLACEHOLDER": "Enter a string", "PROPERTY.FIELDS.IMPLEMENTATION": "Implementation", "PROPERTY.FIELDS.UNSELECTED": "No Field selected", "PROPERTY.FORMPROPERTIES.VALUE": "{{length}} form properties", "PROPERTY.FORMPROPERTIES.EMPTY": "No form properties selected", "PROPERTY.FORMPROPERTIES.ID": "Id", "PROPERTY.FORMPROPERTIES.ID.PLACEHOLDER": "Enter an id", "PROPERTY.FORMPROPERTIES.NAME": "Name", "PROPERTY.FORMPROPERTIES.NAME.PLACEHOLDER": "Enter a name", "PROPERTY.FORMPROPERTIES.TYPE": "Type", "PROPERTY.FORMPROPERTIES.DATEPATTERN": "Date pattern", "PROPERTY.FORMPROPERTIES.DATEPATTERN.PLACEHOLDER": "Enter date pattern", "PROPERTY.FORMPROPERTIES.VALUES": "Values", "PROPERTY.FORMPROPERTIES.ENUMVALUES.EMPTY": "No enum value selected", "PROPERTY.FORMPROPERTIES.VALUES.ID": "Id", "PROPERTY.FORMPROPERTIES.VALUES.NAME": "Name", "PROPERTY.FORMPROPERTIES.VALUES.ID.PLACEHOLDER": "Enter id of a value", "PROPERTY.FORMPROPERTIES.VALUES.NAME.PLACEHOLDER": "Enter name of a value", "PROPERTY.FORMPROPERTIES.EXPRESSION": "Expression", "PROPERTY.FORMPROPERTIES.EXPRESSION.PLACEHOLDER": "Enter an expression", "PROPERTY.FORMPROPERTIES.VARIABLE": "Variable", "PROPERTY.FORMPROPERTIES.VARIABLE.PLACEHOLDER": "Enter a variable", "PROPERTY.FORMPROPERTIES.REQUIRED": "Required", "PROPERTY.FORMPROPERTIES.READABLE": "Readable", "PROPERTY.FORMPROPERTIES.WRITABLE": "Writable", "PROPERTY.INPARAMETERS.VALUE": "{{length}} in-parameters", "PROPERTY.INPARAMETERS.EMPTY": "No in-parameters configured", "PROPERTY.OUTPARAMETERS.VALUE": "{{length}} out-parameters", "PROPERTY.OUTPARAMETERS.EMPTY": "No out-parameters configured", "PROPERTY.PARAMETER.SOURCE": "Source", "PROPERTY.PARAMETER.SOURCE.PLACEHOLDER": "Enter a source", "PROPERTY.PARAMETER.SOURCEEXPRESSION": "Source expression", "PROPERTY.PARAMETER.SOURCEEXPRESSION.PLACEHOLDER": "Enter a source expression", "PROPERTY.PARAMETER.TARGET": "Target", "PROPERTY.PARAMETER.TARGET.PLACEHOLDER": "Enter a target", "PROPERTY.PARAMETER.EMPTY": "No parameter selected", "PROPERTY.SUBPROCESSREFERENCE.EMPTY": "No reference selected", "PROPERTY.SUBPROCESSREFERENCE.TITLE": "Collapsed subprocess reference", "PROPERTY.SUBPROCESSREFERENCE.ERROR.SUBPROCESS": "There was an error loading the subprocesses. Try again later", "PROPERTY.SUBPROCESSREFERENCE.FOLDER.ROOT": "Folders", "PROPERTY.SUBPROCESSREFERENCE.FOLDER.LOADING": "Loading folders...", "PROPERTY.SUBPROCESSREFERENCE.FOLDER.EMPTY": "This folder contains no sub-folders", "PROPERTY.SUBPROCESSREFERENCE.SUBPROCESS.LOADING": "Loading subprocesses...", "PROPERTY.SUBPROCESSREFERENCE.SUBPROCESS.EMPTY": "This folder contains no subprocesses", "PROPERTY.FORMREFERENCE.EMPTY": "No reference selected", "PROPERTY.FORMREFERENCE.TITLE": "Form reference", "PROPERTY.FORMREFERENCE.ERROR.FORM": "There was an error loading the forms. Try again later", "PROPERTY.FORMREFERENCE.FOLDER.ROOT": "Folders", "PROPERTY.FORMREFERENCE.FOLDER.LOADING": "Loading folders...", "PROPERTY.FORMREFERENCE.FOLDER.EMPTY": "This folder contains no sub-folders", "PROPERTY.FORMREFERENCE.FORM.LOADING": "Loading forms...", "PROPERTY.FORMREFERENCE.FORM.EMPTY": "This folder contains no forms", "PROPERTY.TASKLISTENERS.VALUE": "{{length}} task listeners", "PROPERTY.TASKLISTENERS.EMPTY": "No task listeners configured", "PROPERTY.TASKLISTENERS.EVENT": "Event", "PROPERTY.TASKLISTENERS.CLASS": "Class", "PROPERTY.TASKLISTENERS.CLASS.PLACEHOLDER": "Enter a classname", "PROPERTY.TASKLISTENERS.EXPRESSION": "Expression", "PROPERTY.TASKLISTENERS.EXPRESSION.PLACEHOLDER": "Enter an expression", "PROPERTY.TASKLISTENERS.DELEGATEEXPRESSION": "Delegate expression", "PROPERTY.TASKLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER": "Enter a delegate expression", "PROPERTY.TASKLISTENERS.UNSELECTED": "No task listener selected", "PROPERTY.TASKLISTENERS.FIELDS.NAME": "Name", "PROPERTY.TASKLISTENERS.FIELDS.NAME.PLACEHOLDER": "Enter a name", "PROPERTY.TASKLISTENERS.FIELDS.EXPRESSION": "Expression", "PROPERTY.TASKLISTENERS.FIELDS.EXPRESSION.PLACEHOLDER": "Enter an expression", "PROPERTY.TASKLISTENERS.FIELDS.STRINGVALUE": "String value", "PROPERTY.TASKLISTENERS.FIELDS.STRINGVALUE.PLACEHOLDER": "Enter a string value", "PROPERTY.TASKLISTENERS.FIELDS.STRING": "String", "PROPERTY.TASKLISTENERS.FIELDS.STRING.PLACEHOLDER": "Enter a string", "PROPERTY.TASKLISTENERS.FIELDS.IMPLEMENTATION": "Implementation", "PROPERTY.TASKLISTENERS.FIELDS.EMPTY": "No Field selected", "PROPERTY.EVENTLISTENERS.DISPLAY": "{{length}} event listeners", "PROPERTY.EVENTLISTENERS.EMPTY": "No event listeners configured", "PROPERTY.EVENTLISTENERS.EVENTS": "Events", "PROPERTY.EVENTLISTENERS.RETHROW": "Rethrow event?", "PROPERTY.EVENTLISTENERS.CLASS": "Class", "PROPERTY.EVENTLISTENERS.CLASS.PLACEHOLDER": "Enter a classname", "PROPERTY.EVENTLISTENERS.DELEGATEEXPRESSION": "Delegate expression", "PROPERTY.EVENTLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER": "Enter a delegate expression", "PROPERTY.EVENTLISTENERS.ENTITYTYPE": "Entity type", "PROPERTY.EVENTLISTENERS.ENTITYTYPE.PLACEHOLDER": "Enter an entity type", "PROPERTY.EVENTLISTENERS.RETHROWTYPE": "Rethrow event type", "PROPERTY.EVENTLISTENERS.ERRORCODE": "Error code", "PROPERTY.EVENTLISTENERS.ERRORCODE.PLACEHOLDER": "Enter an error code", "PROPERTY.EVENTLISTENERS.MESSAGENAME": "Message name", "PROPERTY.EVENTLISTENERS.MESSAGENAME.PLACEHOLDER": "Enter a message name", "PROPERTY.EVENTLISTENERS.SIGNALNAME": "Signal name", "PROPERTY.EVENTLISTENERS.SIGNALNAME.PLACEHOLDER": "Enter a signal name", "PROPERTY.EVENTLISTENERS.UNSELECTED": "No event listener selected", "PROPERTY.SIGNALDEFINITIONS.DISPLAY": "{{length}} signal definitions", "PROPERTY.SIGNALDEFINITIONS.EMPTY": "No signal definitions configured", "PROPERTY.SIGNALDEFINITIONS.SCOPE-GLOBAL": "Global", "PROPERTY.SIGNALDEFINITIONS.SCOPE-PROCESSINSTANCE": "Process Instance", "PROPERTY.SIGNALDEFINITIONS.ID": "Id", "PROPERTY.SIGNALDEFINITIONS.NAME": "Name", "PROPERTY.SIGNALDEFINITIONS.SCOPE": "<PERSON><PERSON>", "PROPERTY.MESSAGEDEFINITIONS.DISPLAY": "{{length}} message definitions", "PROPERTY.MESSAGEDEFINITIONS.EMPTY": "No message definitions configured", "PROPERTY.MESSAGEDEFINITIONS.ID": "Id", "PROPERTY.MESSAGEDEFINITIONS.NAME": "Name", "PROPERTY.SEQUENCEFLOW.ORDER.EMPTY": "No sequence flow order determined", "PROPERTY.SEQUENCEFLOW.ORDER.NOT.EMPTY": "Sequence flow order set", "PROPERTY.SEQUENCEFLOW.ORDER.NO.OUTGOING.SEQUENCEFLOW.FOUND": "No outgoing sequence flow found.", "PROPERTY.SEQUENCEFLOW.ORDER.DESCRIPTION": "Set the order in which the sequence flow need to be evaluated:", "PROPERTY.SEQUENCEFLOW.ORDER.SEQUENCEFLOW.VALUE": "Sequence flow to {{targetType}} {{targetTitle}}", "PROPERTY.SEQUENCEFLOW.CONDITION.TITLE": "Sequence flow condition", "PROPERTY.SEQUENCEFLOW.CONDITION.TYPE.TITLE": "Condition type", "PROPERTY.SEQUENCEFLOW.CONDITION.TYPE.VARIABLE": "Select variables", "PROPERTY.SEQUENCEFLOW.CONDITION.TYPE.STATIC": "Static value", "PROPERTY.SEQUENCEFLOW.CONDITION.STATIC": "Condition expression", "PROPERTY.SEQUENCEFLOW.CONDITION.STATIC_PLACEHOLDER": "Fill-in expression value", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.TYPE": "Variable type", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-CONDITION": "No condition", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.FORM-FIELD": "Form field", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.FORM-OUTCOME": "Form outcome", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-FIELD": "Select field", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-FIELDS-AVAILABLE": "No fields available", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-FORM": "Select form", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-FORMS-AVAILABLE": "No forms available", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-OPERATOR": "Select operator", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.EQUALS": "Equals", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NOTEQUALS": "Not equals", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.LESSTHAN": "Less than", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.GREATERTHAN": "Greater than", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-OUTCOME": "Select outcome", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-OUTCOMES-AVAILABLE": "No outcomes available", "PROPERTY.SEQUENCEFLOW.CONDITION.NO-CONDITION-DISPLAY": "No condition", "MODEL.SAVE.TITLE": "Save model", "MODEL.NAME": "Name", "MODEL.DESCRIPTION": "Description", "MODEL.SAVE.NEWVERSION": "Save this as a new version?  This means you can always go back to a previous version", "MODEL.SAVE.COMMENT": "Comment", "MODEL.SAVE.SAVING": "Saving model", "MODEL.LASTMODIFIEDDATE": "Last saved", "MODEL.SAVE.ERROR": "Unexpected error: could not save model", "EVENT_TYPE.ACTIVITY.COMPENSATE.TOOLTIP": "An activity is about to be executed as a compensation for another activity. The event targets the activity that is about to be executed for compensation", "EVENT_TYPE.ACTIVITY.COMPLETED.TOOLTIP": "An activity has been completed successfully", "EVENT_TYPE.ACTIVITY.ERROR.RECEIVED.TOOLTIP": "An activity has received an error event. Dispatched before the actual error has been received by the activity", "EVENT_TYPE.MEMBERSHIP.CREATED.TOOLTIP": "A new membership has been created", "EVENT_TYPE.MEMBERSHIP.DELETED.TOOLTIP": "A single membership has been deleted", "EVENT_TYPE.MEMBERSHIPS.DELETED.TOOLTIP": "All memberships in the related group have been deleted. No individual events will be dispatched due to possible performance reasons", "EVENT_TYPE.TASK.ASSIGNED.TOOLTIP": "A task as been assigned. This is thrown alongside with an ENTITY_UPDATED event", "EVENT_TYPE.TASK.COMPLETED.TOOLTIP": "A task has been completed. Dispatched before the task entity is deleted", "EVENT_TYPE.UNCAUGHT.BPMNERROR.TOOLTIP": "When a BPMN Error was thrown, but was not caught within in the process", "EVENT_TYPE.VARIABLE.CREATED.TOOLTIP": "A new variable has been created", "EVENT_TYPE.VARIABLE.DELETED.TOOLTIP": "An existing variable has been deleted", "EVENT_TYPE.VARIABLE.UPDATED.TOOLTIP": "An existing variable has been updated"}