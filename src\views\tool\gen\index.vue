<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="getList"
      @refresh="refresh"
      @search="getList"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['tool:gen:code']"
          :disabled="multiple"
          icon="Download"
          plain
          type="primary"
          @click="handleGenTable"
          >{{ $t('action.generate') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasRole="['admin']" icon="Plus" plain type="primary" @click="openCreateTable"
          >{{ $t('action.create') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['tool:gen:import']"
          icon="Upload"
          plain
          type="info"
          @click="openImportTable"
          >{{ $t('action.import') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['tool:gen:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleEditTable"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['tool:gen:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
    </el-row>

    <art-table
      v-loading="loading"
      :data="tableList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55"></el-table-column>
      <el-table-column :label="$t('number.serial')" align="center" type="index" width="140">
        <template #default="scope">
          <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('name.table')"
        :show-overflow-tooltip="true"
        align="center"
        prop="tableName"
      />
      <el-table-column
        :label="$t('description.table')"
        :show-overflow-tooltip="true"
        align="center"
        prop="tableComment"
      />
      <el-table-column
        :label="$t('entity')"
        :show-overflow-tooltip="true"
        align="center"
        prop="className"
      />
      <el-table-column :label="$t('time.creation')" align="center" prop="createTime" width="160" />
      <el-table-column :label="$t('time.update')" align="center" prop="updateTime" width="160" />
      <el-table-column :label="$t('method.generation')" align="center" prop="genType">
        <template #default="scope">
          <dict-tag :options="gen_type" :value="scope.row.genType" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
        width="330"
      >
        <template #default="scope">
          <el-tooltip :content="$t('action.preview')" placement="top">
            <el-button
              v-hasPermi="['tool:gen:preview']"
              icon="View"
              link
              type="primary"
              @click="handlePreview(scope.row)"
            ></el-button>
          </el-tooltip>
          <el-tooltip :content="$t('action.edit')" placement="top">
            <el-button
              v-hasPermi="['tool:gen:edit']"
              icon="Edit"
              link
              type="primary"
              @click="handleEditTable(scope.row)"
            ></el-button>
          </el-tooltip>
          <el-tooltip :content="$t('action.delete')" placement="top">
            <el-button
              v-hasPermi="['tool:gen:remove']"
              icon="Delete"
              link
              type="danger"
              @click="handleDelete(scope.row)"
            ></el-button>
          </el-tooltip>
          <el-tooltip :content="$t('action.synchronize')" placement="top">
            <el-button
              v-hasPermi="['tool:gen:edit']"
              icon="Refresh"
              link
              type="primary"
              @click="handleSynchDb(scope.row)"
            ></el-button>
          </el-tooltip>
          <el-tooltip :content="$t('code.generate')" placement="top">
            <el-button
              v-hasPermi="['tool:gen:code']"
              icon="Download"
              link
              type="primary"
              @click="handleGenTable(scope.row)"
            ></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </art-table>

    <!-- 预览界面 -->
    <el-dialog
      v-model="preview.open"
      :title="preview.title"
      append-to-body
      class="scrollbar"
      top="5vh"
      width="80%"
    >
      <el-tabs v-model="preview.activeName">
        <el-tab-pane
          v-for="(value, key) in preview.data"
          :key="value"
          :label="key.substring(key.lastIndexOf('/') + 1, key.indexOf('.vm'))"
          :name="key.substring(key.lastIndexOf('/') + 1, key.indexOf('.vm'))"
        >
          <el-link
            v-copyText="value"
            v-copyText:callback="copyTextSuccess"
            :underline="false"
            icon="DocumentCopy"
            style="float: right"
            >{{ $t('action.copy') }}
          </el-link>
          <pre>{{ value }}</pre>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <import-table ref="importRef" @ok="handleQuery" />
    <create-table ref="createRef" @ok="handleQuery" />
  </div>
</template>

<script name="Gen" setup>
  import { getCurrentInstance } from 'vue'
  import { customPath, delTable, genCode, listTable, previewTable, synchDb } from '@/api/tool/gen'
  import { router } from '@/router'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import importTable from './importTable'
  import createTable from './createTable'

  const { proxy } = getCurrentInstance()

  const route = useRoute()
  const { gen_type } = proxy.useDict('gen_type')

  const tableList = ref([])
  const loading = ref(true)
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const tableNames = ref([])
  const dateRange = ref([])
  const uniqueId = ref('')

  const data = reactive({
    fromKey: 'table',
    fromName: 'table',
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      tableName: undefined,
      tableComment: undefined
    },
    preview: {
      open: false,
      title: proxy.$t('preview.code'),
      data: {},
      activeName: 'domain.java'
    }
  })

  const { queryParams, preview } = toRefs(data)

  onActivated(() => {
    const time = route.query.t
    if (time !== null && time !== uniqueId.value) {
      uniqueId.value = time
      queryParams.value.pageNum = Number(route.query.pageNum)
      dateRange.value = []
      proxy.resetForm('queryForm')
      getList()
    }
  })

  /** 查询表集合 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    listTable(proxy.addDateRange(queryParams.value, dateRange.value)).then(({ data }) => {
      tableList.value = data.records
      total.value = data.totalRow
      loading.value = false
    })
  }

  /** 生成代码操作 */
  function handleGenTable(row) {
    const tbNames = row.tableName || tableNames.value
    if (tbNames === '') {
      proxy.$modal.msgError(proxy.$t('data.generate.select'))
      return
    }
    if (row.genType === '1') {
      genCode(row.tableName).then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('path.custom.generated.success') + row.genPath)
      })
    } else if (row.genType === '2') {
      customPath(row.tableName).then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('path.specified.generated.success'))
      })
    } else {
      proxy.$download.zip('/tool/gen/batchGenCode?tables=' + tbNames, 'ruoyi.zip')
    }
  }

  /** 同步数据库操作 */
  function handleSynchDb(row) {
    const tableName = row.tableName
    proxy.$modal
      .confirm(proxy.$t('confirm.force.sync') + tableName + proxy.$t('structure.table.question'))
      .then(function () {
        return synchDb(tableName)
      })
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('synchronization.success'))
      })
      .catch(() => {})
  }

  /** 打开导入表弹窗 */
  function openImportTable() {
    proxy.$refs['importRef'].show()
  }

  /** 打开创建表弹窗 */
  function openCreateTable() {
    proxy.$refs['createRef'].show()
  }

  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = []
    proxy.resetForm('queryRef')
    handleQuery()
  }

  /** 预览按钮 */
  function handlePreview(row) {
    previewTable(row.tableId).then((response) => {
      preview.value.data = response.data
      preview.value.open = true
      preview.value.activeName = 'domain.java'
    })
  }

  /** 复制代码成功 */
  function copyTextSuccess() {
    proxy.$modal.msgSuccess(proxy.$t('copy.success'))
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.tableId)
    tableNames.value = selection.map((item) => item.tableName)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  /** 修改按钮操作 */
  function handleEditTable(row) {
    const tableId = row.tableId || ids.value[0]
    router.push({
      path: '/tool/gen-edit/index/' + tableId,
      query: { pageNum: queryParams.value.pageNum }
    })
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const tableIds = row.tableId || ids.value
    proxy.$modal
      .confirm(proxy.$t('confirm.delete.table.number') + tableIds + proxy.$t('item.data.question'))
      .then(function () {
        return delTable(tableIds)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('delete.success'))
      })
      .catch(() => {})
  }

  const refresh = () => {
    getList()
  }

  getList()
</script>
