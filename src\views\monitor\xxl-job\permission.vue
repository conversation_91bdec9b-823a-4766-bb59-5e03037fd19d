<!--任务管理-->
<template>
  <div class="page-content">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
      <el-form-item :label="$t('type')" prop="role">
        <el-select
          v-model="queryParams.role"
          :placeholder="$t('type.select')"
          style="width: 200px"
          @change="getList"
        >
          <el-option
            v-for="item in typeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('account')" prop="username">
        <el-input v-model="queryParams.username" />
      </el-form-item>

      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">{{
          $t('action.search')
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('action.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="Plus" plain type="primary" @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <!--<el-table v-loading="loading" :data="logList">-->
    <art-table
      v-loading="loading"
      :data="logList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="getList"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column :label="$t('account')" align="center" prop="username" />
      <el-table-column :label="$t('type')" align="center" prop="role">
        <template #default="scope">
          <el-tag v-if="scope.row.role == 1">{{ $t('all') }}</el-tag>

          <el-tag v-else-if="scope.row.role == 0" type="warning">{{ $t('action.specify') }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-dropdown v-if="scope.row.triggerCode !== 500" @command="more">
            <el-button type="primary"
              >{{ $t('operation') }}
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{ type: 'edit', id: scope.row.id }">{{
                  $t('action.edit')
                }}</el-dropdown-item>
                <el-dropdown-item :command="{ type: 'remove', id: scope.row.id }"
                  >{{ $t('action.delete') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </art-table>

    <!--新增执行器-->

    <el-dialog v-model="open" :title="title" append-to-body width="25%" @close="dialogClose">
      <el-form ref="form" :model="dataInfo" :rules="rules">
        <el-form-item :label="$t('username')" prop="username" style="width: 100% !important">
          <template #default="scope">
            <el-select
              v-model="dataInfo.username"
              :disabled="dataInfo.id !== undefined"
              :placeholder="$t('user.select')"
              :remote-method="remoteMethod"
              filterable
              remote
              style="width: 100% !important"
            >
              <el-option
                v-for="item in userList"
                :key="item.id"
                :label="item.nickName"
                :value="item.userName"
              >
                <span style="float: left">{{ item.nickName }}</span>
                <span
                  style="float: right; color: var(--el-text-color-secondary); font-size: 13px"
                  >{{ item.userName }}</span
                >
              </el-option>
            </el-select>
          </template>
          <!--          <el-input placeholder="请输入用户名" v-model="dataInfo.username"/>-->
        </el-form-item>
        <el-form-item :label="$t('role')" prop="role" style="width: 100% !important">
          <el-radio-group v-model="dataInfo.role" @change="roleChange">
            <el-radio v-for="item in typeList.slice(1)" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="dataInfo.role === 0"
          :label="$t('permission')"
          prop="permission"
          style="width: 100% !important"
        >
          <el-checkbox-group v-model="dataInfo.permission">
            <el-checkbox v-for="item in groupList" :label="item.id">{{ item.title }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submit">{{ $t('action.confirm') }}</el-button>
          <el-button @click="open = false">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="Execute_manage" setup>
  import { getCurrentInstance } from 'vue'
  import { getById, list, remove, save, update } from '@/api/job/user'
  import { listAll } from '@/api/job/jobGroup'
  import { searchUser } from '@/api/system/user'

  const { proxy } = getCurrentInstance()

  const logList = ref([])
  const open = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])

  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    typeList: [
      { value: -1, label: proxy.$t('all') },
      { value: 0, label: proxy.$t('action.specify') }
    ],
    dataInfo: {
      username: '',
      password: '123456',
      permission: [],
      role: 0
    },
    groupList: [],
    queryParams: {
      start: 1,
      length: 10,
      role: -1,
      title: ''
    },
    userList: [],
    rules: {
      username: [{ required: true, message: proxy.$t('username.input'), trigger: 'blur' }],
      permission: [{ required: true, message: proxy.$t('permission.select'), trigger: 'change' }]
    }
  })

  const { queryParams, dataInfo, rules, typeList, groupList, userList } = toRefs(data)

  function roleChange(val) {
    if (val === 1) {
      dataInfo.value.permission = []
    }
  }

  function remoteMethod(val) {
    searchUser({ keyword: val }).then((res) => {
      userList.value = res.data
    })
  }

  function dialogClose() {
    reset()
  }

  // 表单重置
  function reset() {
    dataInfo.value = {
      id: undefined,
      username: '',
      password: '123456',
      role: 0
    }
    userList.value = []
    proxy.resetForm('form')
  }

  /**
   * 保存
   */
  function submit() {
    proxy.$refs['form'].validate(async (valid) => {
      if (valid) {
        let value = Object.assign({}, dataInfo.value)
        value.permission = value.permission.join(',')
        if (value.id) {
          update(value).then((res) => {
            proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))

            open.value = false
            getList()
          })
        } else {
          save(value).then((res) => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess(proxy.$t('save.success'))
              open.value = false
              getList()
            }
          })
        }
      }
    })
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.start = 1
    getList()
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef')
    handleQuery()
  }

  /**
   * 显示新增页面
   */
  function handleAdd() {
    title.value = proxy.$t('permissions.configure')
    reset()
    open.value = true
  }

  function getGroupList() {
    listAll().then((res) => {
      groupList.value = res.data
    })
  }

  /**
   * 操作
   */
  function more(val) {
    switch (val.type) {
      case 'edit':
        showEditDialo(val.id)
        break
      case 'remove':
        removeUser(val.id)
        break
    }
  }

  function showEditDialo(id) {
    getById({ id }).then((res) => {
      let value = res.data
      let permissionVal = value.permission
      if (permissionVal) {
        let permission = value.permission.split(',')

        value.permission = []
        permission.forEach((item) => {
          value.permission.push(parseInt(item))
        })
      } else {
        value.permission = []
      }
      value.password = null
      dataInfo.value = value
      title.value = proxy.$t('permission.modify')
      open.value = true
    })
  }

  function removeUser(id) {
    proxy.$modal.confirm(proxy.$t('permission.delete.confirm')).then((res) => {
      remove({ id: id }).then((res) => {
        proxy.$modal.msgSuccess(proxy.$t('operation.success'))
        getList()
      })
    })
  }

  /**
   * 列表
   */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    list(queryParams.value).then((response) => {
      logList.value = response.data
      total.value = response.recordsFiltered
      loading.value = false
    })
  }

  getGroupList()
  getList()
</script>

<style scoped>
  :deep(.el-dialog__header) {
    padding: 0 !important;
    padding-bottom: 0 !important;
    margin-right: 0 !important;
    word-break: break-all;
  }
</style>
