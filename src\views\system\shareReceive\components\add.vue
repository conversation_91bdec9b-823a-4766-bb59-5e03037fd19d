<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="shareReceiveRef" :model="form" :rules="rules">
          <el-row>
            <!--分享ID-->
            <el-col :span="12">
              <el-form-item :label="$t('shareReceive.shareId')" prop="shareId">
                <el-input
                  v-model="form.shareId"
                  :placeholder="formatStr($t('input.please'), $t('shareReceive.shareId'))"
                />
              </el-form-item>
            </el-col>

            <!--接收人ID-->
            <el-col :span="12">
              <el-form-item :label="$t('shareReceive.receiveUserId')" prop="receiveUserId">
                <el-input
                  v-model="form.receiveUserId"
                  :placeholder="formatStr($t('input.please'), $t('shareReceive.receiveUserId'))"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'
  import { computed, toRefs } from 'vue'
  import {
    addShareReceive,
    getShareReceive,
    updateShareReceive
  } from '@/api/system/shareReceive/ShareReceive'

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {},
    rules: {
      shareId: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('shareReceive.shareId'), proxy.$t('not.empty')),
          trigger: ['blur', 'change']
        }
      ],
      receiveUserId: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('shareReceive.receiveUserId'), proxy.$t('not.empty')),
          trigger: ['blur', 'change']
        }
      ]
    }
  })

  const { form, rules } = toRefs(data)
  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['shareReceiveRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true

        if (form.value.id != null) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateShareReceive(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }
  const add = () => {
    addShareReceive(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))

        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getShareReceive(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      shareId: null,
      receiveUserId: null
    }
    proxy.resetForm('shareReceiveRef')
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }
</script>
