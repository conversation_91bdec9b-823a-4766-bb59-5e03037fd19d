<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="getList"
      @refresh="refresh"
      @search="getList"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:logininfor:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:logininfor:remove']"
          icon="Delete"
          plain
          type="danger"
          @click="handleClean"
          >{{ $t('action.clear') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:logininfor:unlock']"
          :disabled="single"
          icon="Unlock"
          plain
          type="primary"
          @click="handleUnlock"
          >{{ $t('action.unlock') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:logininfor:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
      <right-toolbar @queryTable="getList"></right-toolbar>
    </el-row>

    <art-table
      ref="logininforRef"
      v-loading="loading"
      :data="logininforList"
      :default-sort="defaultSort"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      row-key="infoId"
      @search="getList"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column :label="$t('access.number')" align="center" prop="infoId" />
      <el-table-column
        :label="$t('name.user')"
        :show-overflow-tooltip="true"
        :sort-orders="['descending', 'ascending']"
        align="center"
        prop="userName"
        sortable="custom"
      />
      <el-table-column
        :label="$t('address')"
        :show-overflow-tooltip="true"
        align="center"
        prop="ipaddr"
      />
      <el-table-column
        :label="$t('location.login')"
        :show-overflow-tooltip="true"
        align="center"
        prop="loginLocation"
      />
      <el-table-column
        :label="$t('system.operating')"
        :show-overflow-tooltip="true"
        align="center"
        prop="os"
      />
      <el-table-column
        :label="$t('browser')"
        :show-overflow-tooltip="true"
        align="center"
        prop="browser"
      />
      <el-table-column :label="$t('status.login')" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_common_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('description')"
        :show-overflow-tooltip="true"
        align="center"
        prop="msg"
      />
      <el-table-column
        :label="$t('time.access')"
        :sort-orders="['descending', 'ascending']"
        align="center"
        prop="loginTime"
        sortable="custom"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.loginTime) }}</span>
        </template>
      </el-table-column>
    </art-table>
  </div>
</template>

<script name="Logininfor" setup>
  import { getCurrentInstance } from 'vue'
  import { cleanLogininfor, delLogininfor, list, unlockLogininfor } from '@/api/monitor/logininfor'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'

  const { proxy } = getCurrentInstance()

  const { sys_common_status } = proxy.useDict('sys_common_status')

  const logininforList = ref([])
  const loading = ref(true)
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const selectName = ref('')
  const total = ref(0)
  const dateRange = ref([])
  const defaultSort = ref({ prop: 'loginTime', order: 'descending' })

  const data = reactive({
    fromKey: 'logininfor',
    fromName: 'logininfor',
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      ipaddr: undefined,
      userName: undefined,
      status: undefined,
      orderByColumn: undefined,
      isAsc: undefined
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询登录日志列表 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    list(proxy.addDateRange(queryParams.value, dateRange.value)).then(({ data }) => {
      logininforList.value = data.records
      total.value = data.totalRow
      loading.value = false
    })
  }



  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.infoId)
    multiple.value = !selection.length
    single.value = selection.length !== 1
    selectName.value = selection.map((item) => item.userName)
  }

  /** 排序触发事件 */
  function handleSortChange(column, prop, order) {
    queryParams.value.orderByColumn = column.prop
    queryParams.value.isAsc = column.order
    getList()
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const infoIds = row.infoId || ids.value
    proxy.$modal
      .confirm(proxy.$t('confirm.delete.number.access') + infoIds + proxy.$t('item.data.question'))
      .then(function () {
        return delLogininfor(infoIds)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 清空按钮操作 */
  function handleClean() {
    proxy.$modal
      .confirm(proxy.$t('confirm.clear.log.login'))
      .then(function () {
        return cleanLogininfor()
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('clear.success'))
      })
      .catch(() => {})
  }

  /** 解锁按钮操作 */
  function handleUnlock() {
    const username = selectName.value
    proxy.$modal
      .confirm(proxy.$t('user.unlock.confirm') + username + proxy.$t('item.data.question'))
      .then(function () {
        return unlockLogininfor(username)
      })
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('user') + username + proxy.$t('unlock.successful'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'monitor/logininfor/export',
      {
        ...queryParams.value
      },
      `config_${new Date().getTime()}.xlsx`
    )
  }

  const refresh = () => {
    getList()
  }

  getList()
</script>
