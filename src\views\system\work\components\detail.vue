<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <component :is="componentName" ref="workForm" />
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import Drawer from '@/components/Drawer/Drawer.vue'
  import Bigtian from '@/views/system/work/components/bigtian.vue'
  import Other from '@/views/system/work/components/other.vue'
  import { computed, toRefs } from 'vue'
  import { getWork } from '@/api/system/work/Work'

  const { work_status, work_type } = proxy.useDict('work_status', 'work_type')
  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    workType: {
      type: String,
      request: true
    },
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)
  const componentName = computed(() => {
    switch (props.workType) {
      case 'bigtian':
        return Bigtian
      default:
        return Other
    }
  })

  const init = () => {
    if (props.id) {
      getWork(props.id).then((response) => {
        data.form = response.data
      })
    }
  }
  const loadingBtn = reactive({
    submit: false
  })
  const submitForm = () => {
    proxy.$refs.workForm.submitForm()
    loadingBtn.submit = true
  }
</script>
