<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="search"
      @refresh="refresh"
      @search="search"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:products:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:products:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:products:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:products:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
    </el-row>

    <art-table
      v-loading="loading"
      :data="productsList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="search"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <!--${comment}-->
      <!--产品名称-->
      <el-table-column :label="$t('products.name')" :show-overflow-tooltip="true" prop="name">
        <template #default="{ row }">
          <el-link type="primary" @click="visibleDetail(row)">{{ row.name }}</el-link>
        </template>
      </el-table-column>
      <!--副标题/卖点-->
      <el-table-column
        :label="$t('products.subtitle')"
        :show-overflow-tooltip="true"
        prop="subtitle"
      />
      <!--产品描述-->
      <!--<el-table-column :label="$t('products.description')" prop="description" :show-overflow-tooltip="true"/>-->
      <!--首页封面图-->
      <el-table-column
        :label="$t('products.coverImage')"
        :show-overflow-tooltip="true"
        prop="coverImage"
      >
        <template #default="scope">
          <image-preview :height="50" :src="scope.row.coverImage" :width="50" />
        </template>
      </el-table-column>
      <!--详情页图片，支持多张-->
      <el-table-column
        :label="$t('products.detailImages')"
        :show-overflow-tooltip="true"
        prop="detailImages"
      >
        <template #default="scope">
          <image-preview :height="50" :src="scope.row.detailImages" :width="50" />
        </template>
      </el-table-column>
      <!--原价-->
      <el-table-column
        :label="$t('products.originalPrice')"
        :show-overflow-tooltip="true"
        prop="originalPrice"
      />
      <!--规格-->
      <el-table-column
        :label="$t('products.specType')"
        :show-overflow-tooltip="true"
        prop="specType"
      >
        <template #default="scope">
          <dict-tag :options="spec_type" :value="scope.row.specType" />
        </template>
      </el-table-column>
      <!--现价/销售价-->
      <el-table-column
        :label="$t('products.sellingPrice')"
        :show-overflow-tooltip="true"
        prop="sellingPrice"
      />
      <!--预售价-->
      <el-table-column
        :label="$t('products.presalePrice')"
        :show-overflow-tooltip="true"
        prop="presalePrice"
      />
      <!--总库存-->
      <el-table-column
        :label="$t('products.totalStock')"
        :show-overflow-tooltip="true"
        prop="totalStock"
      />
      <!--状态:delist=下架,listing=上架,pre_sales=预售中-->
      <el-table-column :label="$t('products.status')" :show-overflow-tooltip="true" prop="status">
        <template #default="scope">
          <dict-tag :options="product_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <!--是否推荐:N=否,Y=是-->
      <el-table-column
        :label="$t('products.isRecommend')"
        :show-overflow-tooltip="true"
        prop="isRecommend"
      >
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.isRecommend" />
        </template>
      </el-table-column>
      <!--是否新品:N=否,Y=是-->
      <el-table-column :label="$t('products.isNew')" :show-overflow-tooltip="true" prop="isNew">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.isNew" />
        </template>
      </el-table-column>
      <!--是否热销:N=否,Y=是-->
      <el-table-column :label="$t('products.isHot')" :show-overflow-tooltip="true" prop="isHot">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.isHot" />
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('table.operation')"
        class-name="small-padding fixed-width"
        width="180px"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['system:products:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            >{{ $t('action.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:products:remove']"
            icon="Delete"
            link
            type="danger"
            @click="handleDelete(scope.row)"
            >{{ $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改产品对话框 -->

    <add
      :id="id"
      v-model:show="open"
      :title="title"
      @refreshList="refreshList"
      @close="handleClose"
    />
    <!-- 详情产品对话框 -->
    <detail :id="id" v-model:show="detailShow" :title="title" @close="handleDetailClose" />
  </div>
</template>

<script name="Products" setup>
  import { listDept } from '@/api/system/dept'
  import { delProducts, listProducts } from '@/api/system/products/Products'
  import { listUser } from '@/api/system/user'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import Add from './components/add.vue'
  import Detail from './components/detail.vue'

  const { proxy } = getCurrentInstance()
  const { product_status, sys_yes_no, spec_type } = proxy.useDict(
    'product_status',
    'sys_yes_no',
    'spec_type'
  )

  const productsList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const id = ref()
  const names = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    fromKey: 't_products',
    fromName: '产品',
    deptList: [],
    userList: [],

    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  function refresh(query) {
    query.pageNum = 1
    search(query)
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }

  function search(query) {
    if (query) {
      data.queryParams = { ...data.queryParams, ...query }
    }
    loading.value = true
    listProducts(data.queryParams)
      .then(({ data }) => {
        productsList.value = data.records
        total.value = data.totalRow
        loading.value = false
      })
      .catch((err) => {
        loading.value = false
      })
  }

  // 取消按钮
  function cancel() {
    open.value = false
    reset()
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id)
    names.value = selection.map((item) => item.name)

    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  /** 新增按钮操作 */
  function handleAdd() {
    handleClose()
    nextTick(() => {
      id.value = undefined
      open.value = true
      title.value = proxy.formatStr(proxy.$t('info.add'), proxy.$t('menu.products'))
    })
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    id.value = row.id || ids.value
    title.value = proxy.formatStr(proxy.$t('action.modify') + ' ' + proxy.$t('menu.products'))
    open.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const idKeys = row.id || ids.value
    const nameArr = row.name || names.value
    proxy.$modal
      .confirm(proxy.$t('confirm.delete', { name: nameArr }))
      .then(function () {
        return delProducts(idKeys)
      })
      .then(() => {
        search()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/products/export',
      {
        ...queryParams.value
      },
      `products_${new Date().getTime()}.xlsx`
    )
  }

  const refreshList = (pageNum) => {
    if (pageNum) {
      data.queryParams.pageNum = 1
    }
    search()
  }

  const visibleDetail = (row) => {
    handleDetailClose()
    nextTick(() => {
      id.value = row.id
      title.value = proxy.formatStr(proxy.$t('action.view'), proxy.$t('menu.products'))
      detailShow.value = true
    })
  }

  const handleClose = () => {
    open.value = false
    id.value = undefined
    title.value = ''
  }

  /** 处理详情弹窗关闭 */
  const handleDetailClose = () => {
    detailShow.value = false
    id.value = undefined
    title.value = ''
  }
</script>
