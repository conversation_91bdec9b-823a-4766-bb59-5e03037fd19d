
<div class="modal" ng-controller="KisBpmEventListenersPopupCtrl">
    <div class="modal-dialog modal-wide">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" ng-click="close()">&times;</button>
                <h2>{{'PROPERTY.PROPERTY.EDIT.TITLE' | translate:property}}</h2>
            </div>
            <div class="modal-body">
            
                <div class="row row-no-gutter">
                	<div class="col-xs-10">
            	        <div ng-if="translationsRetrieved" class="kis-listener-grid" ng-grid="gridOptions"></div>
            	        <div class="pull-right">
            	            <div class="btn-group">
            	                <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{ACTION.MOVE.UP | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="moveListenerUp()"><i class="glyphicon glyphicon-arrow-up"></i></a>
            	                <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{ACTION.MOVE.DOWN | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="moveListenerDown()"><i class="glyphicon glyphicon-arrow-down"></i></a>
            	            </div>
            	            <div class="btn-group">
            	                <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{ACTION.ADD | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="addNewListener()"><i class="glyphicon glyphicon-plus"></i></a>
            	                <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{ACTION.REMOVE | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="removeListener()"><i class="glyphicon glyphicon-minus"></i></a>
            	            </div>
            	        </div>
            		</div>
            	</div>
            	
            	<div class="row row-no-gutter">
                  	<div ng-if="translationsRetrieved" ng-show="selectedListeners.length > 0" class="col-xs-6">
            			<div class="form-group">
            	        	<label for="userField">{{'PROPERTY.EVENTLISTENERS.EVENTS' | translate}}</label>
            	            <div ng-repeat="eventDefinition in selectedListeners[0].events">
            	            	<select id="eventField" class="form-control" ng-model="eventDefinition.event" ng-change="listenerDetailsChanged()">
            	            		<option title="{{'EVENT_TYPE.ACTIVITY.COMPENSATE.TOOLTIP' | translate}}">ACTIVITY_COMPENSATE</option>
            	            		<option title="{{'EVENT_TYPE.ACTIVITY.COMPLETED.TOOLTIP' | translate}}">ACTIVITY_COMPLETED</option>
            	            		<option title="bla">ACTIVITY_ERROR_RECEIVED</option>
            	            		<option>ACTIVITY_MESSAGE_RECEIVED</option>
            	            		<option>ACTIVITY_SIGNALED</option>
            	            		<option>ACTIVITY_STARTED</option>
            	            		<option>ENGINE_CLOSED</option>
            	            		<option>ENGINE_CREATED</option>
            	            		<option>ENTITY_ACTIVATED</option>
            	                	<option>ENTITY_CREATED</option>
            	                	<option>ENTITY_DELETED</option>
            	                	<option>ENTITY_INITIALIZED</option>
            	                	<option>ENTITY_SUSPENDED</option>
            	                	<option>ENTITY_UPDATED</option>
            	                	<option>JOB_EXECUTION_FAILURE</option>
            	                	<option>JOB_EXECUTION_SUCCESS</option>
            	                	<option>JOB_RETRIES_DECREMENTED</option>
            	                	<option title="{{'EVENT_TYPE.MEMBERSHIP.CREATED.TOOLTIP' | translate}}">MEMBERSHIP_CREATED</option>
            	                	<option title="{{'EVENT_TYPE.MEMBERSHIP.DELETED.TOOLTIP' | translate}}">MEMBERSHIP_DELETED</option>
            	                	<option title="{{'EVENT_TYPE.MEMBERSHIPS.DELETED.TOOLTIP' | translate}}">MEMBERSHIPS_DELETED</option>
            	                	<option title="{{'EVENT_TYPE.TASK.ASSIGNED.TOOLTIP' | translate}}">TASK_ASSIGNED</option>
            	                	<option title="{{'EVENT_TYPE.TASK.COMPLETED.TOOLTIP' | translate}}">TASK_COMPLETED</option>
            	                	<option>TIMER_FIRED</option>
            	                	<option title="{{'EVENT_TYPE.UNCAUGHT.BPMNERROR.TOOLTIP' | translate}}">UNCAUGHT_BPMN_ERROR</option>
            	                	<option title="{{'EVENT_TYPE.VARIABLE.CREATED.TOOLTIP' | translate}}">VARIABLE_CREATED</option>
            	                	<option title="{{'EVENT_TYPE.VARIABLE.DELETED.TOOLTIP' | translate}}">VARIABLE_DELETED</option>
            	                	<option title="{{'EVENT_TYPE.VARIABLE.UPDATED.TOOLTIP' | translate}}">VARIABLE_UPDATED</option>
            	               	</select>
            		            <i ng-if="$index > 0" class="glyphicon glyphicon-minus clickable-property" ng-click="removeEventValue($index)"></i>
            		            <i class="glyphicon glyphicon-plus clickable-property" ng-click="addEventValue($index)"></i>
            	            </div>
            	            <div class="form-group">
            			   		<label for="classField">{{'PROPERTY.EVENTLISTENERS.RETHROW' | translate}}</label>
            			   		<input type="checkbox" id="rethrowField" class="form-control" ng-model="selectedListeners[0].rethrowEvent" ng-change="listenerDetailsChanged()" />
            				</div>
            	       	</div>
                     </div>
                     <div ng-show="selectedListeners.length > 0 && selectedListeners[0].events[0].event" class="col-xs-6">
                     	<div class="form-group" ng-if="!selectedListeners[0].rethrowEvent">
            		   		<label for="classField">{{'PROPERTY.EVENTLISTENERS.CLASS' | translate}}</label>
            		   		<input type="text" id="classField" class="form-control" ng-model="selectedListeners[0].className" ng-change="listenerDetailsChanged()" placeholder="{{'PROPERTY.EVENTLISTENERS.CLASS.PLACEHOLDER' | translate}}" />
            			</div>
            			<div class="form-group" ng-if="!selectedListeners[0].rethrowEvent">
            		   		<label for="delegateExpressionField">{{'PROPERTY.EVENTLISTENERS.DELEGATEEXPRESSION' | translate}}</label>
            		   		<input type="text" id="delegateExpressionField" class="form-control" ng-model="selectedListeners[0].delegateExpression" ng-change="listenerDetailsChanged()" placeholder="{{'PROPERTY.EVENTLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER' | translate}}" />
            			</div>
            			<div class="form-group" ng-if="!selectedListeners[0].rethrowEvent">
            		   		<label for="entityTypeField">{{'PROPERTY.EVENTLISTENERS.ENTITYTYPE' | translate}}</label>
            		   		<input type="text" id="entityTypeField" class="form-control" ng-model="selectedListeners[0].entityType" ng-change="listenerDetailsChanged()" placeholder="{{'PROPERTY.EVENTLISTENERS.ENTITYTYPE.PLACEHOLDER' | translate}}" />
            			</div>
            			<div class="form-group" ng-if="selectedListeners[0].rethrowEvent">
            		   		<label for="delegateExpressionField">{{'PROPERTY.EVENTLISTENERS.RETHROWTYPE' | translate}}</label>
            		   		<select id="rethrowTypeField" class="form-control" ng-model="selectedListeners[0].rethrowType" ng-change="rethrowTypeChanged()">
                                <option>error</option>
                                <option>message</option>
                                <option>signal</option>
                                <option>globalSignal</option>
                            </select>
            			</div>
            			<div class="form-group" ng-if="selectedListeners[0].rethrowType === 'error'">
            		   		<label for="errorCodeField">{{'PROPERTY.EVENTLISTENERS.ERRORCODE' | translate}}</label>
            		   		<input type="text" id="errorCodeField" class="form-control" ng-model="selectedListeners[0].errorcode" ng-change="listenerDetailsChanged()" placeholder="{{'PROPERTY.EVENTLISTENERS.ERRORCODE.PLACEHOLDER' | translate}}" />
            			</div>
            			<div class="form-group" ng-if="selectedListeners[0].rethrowType === 'message'">
            		   		<label for="messageNameField">{{'PROPERTY.EVENTLISTENERS.MESSAGENAME' | translate}}</label>
            		   		<input type="text" id="messageNameField" class="form-control" ng-model="selectedListeners[0].messagename" ng-change="listenerDetailsChanged()" placeholder="{{'PROPERTY.EVENTLISTENERS.MESSAGENAME.PLACEHOLDER' | translate}}" />
            			</div>
            			<div class="form-group" ng-if="selectedListeners[0].rethrowType === 'signal' || selectedListeners[0].rethrowType === 'globalSignal'">
            		   		<label for="messageNameField">{{'PROPERTY.EVENTLISTENERS.SIGNALNAME' | translate}}</label>
            		   		<input type="text" id="signalNameField" class="form-control" ng-model="selectedListeners[0].signalname" ng-change="listenerDetailsChanged()" placeholder="{{'PROPERTY.EVENTLISTENERS.SIGNALNAME.PLACEHOLDER' | translate}}" />
            			</div>
                     </div>
                     <div ng-show="selectedListeners.length == 0" class="col-xs-6 muted no-property-selected" translate>PROPERTY.EVENTLISTENERS.UNSELECTED</div>
                </div>
            
            </div>
            <div class="modal-footer">
                <button ng-click="cancel()" class="btn btn-primary" translate>ACTION.CANCEL</button>
                <button ng-click="save()" class="btn btn-primary" translate>ACTION.SAVE</button>
            </div>
        </div>
    </div>
</div>