import { io, Socket } from 'socket.io-client'

export default class SocketIo {
  private static instance: SocketIo | null = null
  private socket: Socket | null = null

  private constructor() {}

  // 获取单例实例
  public static getInstance(): Socket | null {
    if (!SocketIo.instance) {
      SocketIo.instance = new SocketIo()
      SocketIo.instance.initSocket()
    }
    return SocketIo.instance.socket
  }

  // 获取 socket 实例
  public getSocket(): Socket | null {
    return this.socket
  }

  // 初始化 WebSocket 连接
  private initSocket(): void {
    if (!this.socket) {
      this.socket = io(import.meta.env.VITE_SOCKET_URL, {
        transports: ['websocket']
      })
      this.socket.on('connect', () => {
        console.log('ws 已连接')
      })

      this.socket.on('connect_error', (error: Error) => {
        console.log('连接失败', error)
      })
    }
  }
}
