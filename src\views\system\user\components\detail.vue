<template>
  <Drawer v-model="open" @open="init" :size="700">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form label-width="120px" style="width: 100%;">
          <el-row :gutter="0">
            <!-- 用户编号 -->
            <el-col :span="12">
              <el-form-item label="用户编号">
                <span>{{ form.userId }}</span>
              </el-form-item>
            </el-col>
            <!-- 用户名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.user')">
                <span>{{ form.userName }}</span>
              </el-form-item>
            </el-col>
            <!-- 用户昵称 -->
            <el-col :span="12">
              <el-form-item :label="$t('nickname.user')">
                <span>{{ form.nickName }}</span>
              </el-form-item>
            </el-col>
            <!-- 归属部门 -->
            <el-col :span="12">
              <el-form-item :label="$t('department.affiliation')">
                <span>{{ form.dept?.deptName }}</span>
              </el-form-item>
            </el-col>
            <!-- 手机号码 -->
            <el-col :span="12">
              <el-form-item :label="$t('number.mobile')">
                <span>{{ form.phonenumber }}</span>
              </el-form-item>
            </el-col>
            <!-- 邮箱 -->
            <el-col :span="12">
              <el-form-item :label="$t('email')">
                <span>{{ form.email }}</span>
              </el-form-item>
            </el-col>
            <!-- 用户性别 -->
            <el-col :span="12">
              <el-form-item :label="$t('gender.user')">
                <dict-tag :options="sys_user_sex" :value="form.sex" />
              </el-form-item>
            </el-col>
            <!-- 状态 -->
            <el-col :span="12">
              <el-form-item :label="$t('status')">
                <dict-tag :options="sys_normal_disable" :value="form.status" />
              </el-form-item>
            </el-col>
            <!-- 创建时间 -->
            <el-col :span="12">
              <el-form-item label="创建时间">
                <span>{{ parseTime(form.createTime) }}</span>
              </el-form-item>
            </el-col>
            <!-- 最后登录IP -->
            <el-col :span="12">
              <el-form-item label="最后登录IP">
                <span>{{ form.loginIp }}</span>
              </el-form-item>
            </el-col>
            <!-- 最后登录时间 -->
            <el-col :span="12">
              <el-form-item label="最后登录时间">
                <span>{{ parseTime(form.loginDate) }}</span>
              </el-form-item>
            </el-col>
            <!-- 岗位 -->
            <el-col :span="24">
              <el-form-item :label="$t('post')">
                <el-tag
                  v-for="post in form.posts"
                  :key="post.postId"
                  type="success"
                  style="margin-right: 5px"
                >
                  {{ post.postName }}
                </el-tag>
                <span v-if="!form.posts || form.posts.length === 0">-</span>
              </el-form-item>
            </el-col>
            <!-- 角色 -->
            <el-col :span="24">
              <el-form-item :label="$t('role')">
                <el-tag
                  v-for="role in form.roles"
                  :key="role.roleId"
                  type="primary"
                  style="margin-right: 5px"
                >
                  {{ role.roleName }}
                </el-tag>
                <span v-if="!form.roles || form.roles.length === 0">-</span>
              </el-form-item>
            </el-col>
            <!-- 备注 -->
            <el-col :span="24">
              <el-form-item :label="$t('remarks')">
                <span>{{ form.remark || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { getUser } from '@/api/system/user'

  const { proxy } = getCurrentInstance()
  const { sys_user_sex, sys_normal_disable } = proxy.useDict('sys_user_sex', 'sys_normal_disable')

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    reset()
    if (props.id) {
      getUser(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {}
  }
</script>
