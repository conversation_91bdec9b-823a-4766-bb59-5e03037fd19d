<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="productCategoriesRef" :model="form" :rules="rules">
          <el-row>
            <!--分类名称-->
            <el-col :span="12">
              <el-form-item :label="$t('productCategories.name')" prop="name">
                <el-input
                  v-model="form.name"
                  :placeholder="formatStr($t('input.please'), $t('productCategories.name'))"
                />
              </el-form-item>
            </el-col>

            <!--分类图标-->
            <el-col :span="12">
              <el-form-item :label="$t('productCategories.icon')">
                <image-upload v-model="form.icon" />
              </el-form-item>
            </el-col>

            <!--排序-->
            <el-col :span="12">
              <el-form-item :label="$t('productCategories.sortOrder')" prop="sortOrder">
                <el-input
                  v-model="form.sortOrder"
                  :placeholder="formatStr($t('input.please'), $t('productCategories.sortOrder'))"
                />
              </el-form-item>
            </el-col>

            <!--状态:0=禁用,1=启用-->
            <el-col :span="12">
              <el-form-item :label="$t('productCategories.status')">
                <el-radio-group v-model="form.status">
                  <el-radio label="1">{{ $t('select.please.dict') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'
  import { computed, toRefs } from 'vue'
  import {
    addProductCategories,
    getProductCategories,
    updateProductCategories
  } from '@/api/system/productCategories/ProductCategories'

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {},
    rules: {
      name: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('productCategories.name'), proxy.$t('not.empty')),
          trigger: 'blur'
        }
      ],
      status: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('productCategories.status'), proxy.$t('not.empty')),
          trigger: 'blur'
        }
      ]
    }
  })

  const { form, rules } = toRefs(data)
  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['productCategoriesRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true

        if (form.value.id != null) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateProductCategories(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }
  const add = () => {
    addProductCategories(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))

        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getProductCategories(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      name: null,
      icon: null,
      sortOrder: null,
      status: '0'
    }
    proxy.resetForm('productCategoriesRef')
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }
</script>
