<template>
  <div class="card art-custom-card">
    <div class="card-header">
      <div class="title">
        <h4 class="box-title">{{ $t('dynamic') }}</h4>
        <p class="subtitle">{{ $t('action.add.new') }}<span>+6</span></p>
      </div>
    </div>

    <div class="list">
      <div v-for="(item, index) in list" :key="index">
        <span class="user">{{ item.username }}</span>
        <span class="type">{{ item.type }}</span>
        <span class="target">{{ item.target }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { getCurrentInstance } from 'vue'
  import { reactive } from 'vue-demi'

  const { proxy } = getCurrentInstance()

  const list = reactive([
    {
      username: proxy.$t('fish.medium'),
      type: proxy.$t('followed'),
      target: proxy.$t('duo.duo.yin')
    },
    {
      username: proxy.$t('he.xiaohe'),
      type: proxy.$t('article.publish'),
      target: proxy.$t('notes.project.practical')
    },
    {
      username: proxy.$t('duo.duo.yin'),
      type: proxy.$t('question.ask'),
      target: proxy.$t('theme.configurable')
    },
    {
      username: proxy.$t('grass.daydreaming'),
      type: proxy.$t('items.exchanged'),
      target: proxy.$t('book.extraordinary.life')
    },
    {
      username: proxy.$t('food.ice.cream.cone'),
      type: proxy.$t('issue.closed'),
      target: proxy.$t('grass.daydreaming')
    },
    {
      username: proxy.$t('leng.yue.dai.dai'),
      type: proxy.$t('items.exchanged'),
      target: proxy.$t('book.effective.habits')
    }
  ])
</script>

<style lang="scss" scoped>
  .card {
    box-sizing: border-box;
    width: 100%;
    height: 510px;
    padding: 0 25px;

    .header {
      display: flex;
      justify-content: space-between;
      padding: 20px 0 0;
    }

    .list {
      height: calc(100% - 100px);
      margin-top: 10px;
      overflow: hidden;

      > div {
        height: 70px;
        overflow: hidden;
        line-height: 70px;
        border-bottom: 1px solid var(--art-border-color);

        span {
          font-size: 13px;
        }

        .user {
          font-weight: 500;
          color: var(--art-text-gray-800);
        }

        .type {
          margin: 0 8px;
        }

        .target {
          color: var(--main-color);
        }
      }
    }
  }
</style>
