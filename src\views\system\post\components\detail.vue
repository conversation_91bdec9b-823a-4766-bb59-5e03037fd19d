<template>
  <Drawer v-model="open" @open="init" :size="600">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form label-width="120px" style="width: 100%;">
          <el-row :gutter="0">
            <!-- 岗位编号 -->
            <el-col :span="12">
              <el-form-item :label="$t('number.post')">
                <span>{{ form.postId }}</span>
              </el-form-item>
            </el-col>
            <!-- 岗位名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.post')">
                <span>{{ form.postName }}</span>
              </el-form-item>
            </el-col>
            <!-- 岗位编码 -->
            <el-col :span="12">
              <el-form-item :label="$t('code.post')">
                <span>{{ form.postCode }}</span>
              </el-form-item>
            </el-col>
            <!-- 岗位顺序 -->
            <el-col :span="12">
              <el-form-item :label="$t('sequence.post')">
                <span>{{ form.postSort }}</span>
              </el-form-item>
            </el-col>
            <!-- 岗位状态 -->
            <el-col :span="12">
              <el-form-item :label="$t('status.post')">
                <dict-tag :options="sys_normal_disable" :value="form.status" />
              </el-form-item>
            </el-col>
            <!-- 创建时间 -->
            <el-col :span="12">
              <el-form-item :label="$t('time.creation')">
                <span>{{ parseTime(form.createTime) }}</span>
              </el-form-item>
            </el-col>
            <!-- 更新时间 -->
            <el-col :span="12">
              <el-form-item :label="$t('time.update')">
                <span>{{ parseTime(form.updateTime) }}</span>
              </el-form-item>
            </el-col>
            <!-- 备注 -->
            <el-col v-if="form.remark" :span="24">
              <el-form-item :label="$t('remarks')">
                <span>{{ form.remark || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { getPost } from '@/api/system/post'

  const { proxy } = getCurrentInstance()
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    reset()
    if (props.id) {
      getPost(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {}
  }
</script>

<style scoped>
/* 组件样式 */
</style>
