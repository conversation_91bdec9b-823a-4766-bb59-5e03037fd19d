<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="search"
      @refresh="refresh"
      @search="search"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:work:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:work:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:work:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:work:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
    </el-row>

    <art-table
      v-loading="loading"
      :data="workList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="search"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column :label="$t('work.title')" :show-overflow-tooltip="true" prop="title">
        <template #default="{ row }">
          <el-link type="primary" @click="visibleDetail(row)">{{ row.title }}</el-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('work.busiKey')" :show-overflow-tooltip="true" prop="busiKey" />
      <el-table-column :label="$t('work.type')" :show-overflow-tooltip="true" prop="type">
        <template #default="scope">
          <dict-tag :options="work_type" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('work.sender')" :show-overflow-tooltip="true" prop="senderText" />
      <el-table-column
        :label="$t('work.receiver')"
        :show-overflow-tooltip="true"
        prop="receiverText"
      />
      <el-table-column :label="$t('work.status')" :show-overflow-tooltip="true" prop="status">
        <template #default="scope">
          <dict-tag :options="work_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.operation')" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)">
            <svg-icon class="big-mr-2" icon-class="monitor" />
            {{ $t('flow. monitoring') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改待办对话框 -->

    <add
      :id="id"
      v-model:show="open"
      :title="title"
      @refreshList="refreshList"
      @close="handleClose"
    />

    <!-- 详情待办对话框 -->
    <detail :id="id" v-model:show="detailShow" :title="title" :workType="workType" />
  </div>
</template>

<script name="Work" setup>
  import { delWork, listWork } from '@/api/system/work/Work'
  import Add from './components/add.vue'
  import Detail from './components/detail.vue'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'

  const { proxy } = getCurrentInstance()
  const { work_status, work_type } = proxy.useDict('work_status', 'work_type')

  const workList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const id = ref()
  const names = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')
  const workType = ref('')

  const data = reactive({
    fromKey: 't_work',
    fromName: '待办',
    deptList: [],
    userList: [],

    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  function refresh(query) {
    query.pageNum = 1
    search(query)
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }

  function search(query) {
    if (query) {
      data.queryParams = { ...data.queryParams, ...query }
    }
    loading.value = true
    listWork(data.queryParams)
      .then(({ data }) => {
        workList.value = data.records
        total.value = data.totalRow
        loading.value = false
      })
      .catch((err) => {
        loading.value = false
      })
  }

  // 取消按钮
  function cancel() {
    open.value = false
    reset()
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id)
    names.value = selection.map((item) => item.title)

    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  /** 新增按钮操作 */
  function handleAdd() {
    handleClose()
    nextTick(() => {
      id.value = undefined
      open.value = true
      title.value = proxy.formatStr(proxy.$t('info.add'), proxy.$t('menu.work'))
    })
  }

  const handleClose = () => {
    open.value = false
    id.value = undefined
    title.value = ''
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    id.value = row.id || ids.value
    title.value = proxy.formatStr(proxy.$t('flow. monitoring'), row.title)
    open.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const idKeys = row.id || ids.value
    const nameArr = row.title || names.value
    proxy.$modal
      .confirm(proxy.$t('confirm.delete', { name: nameArr }))
      .then(function () {
        return delWork(idKeys)
      })
      .then(() => {
        search()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/work/export',
      {
        ...queryParams.value
      },
      `work_${new Date().getTime()}.xlsx`
    )
  }

  const refreshList = (pageNum) => {
    if (pageNum) {
      data.queryParams.pageNum = 1
    }
    search()
  }

  const visibleDetail = (row) => {
    id.value = row.id
    workType.value = row.type
    title.value = proxy.formatStr(proxy.$t('action.view'), proxy.$t('menu.work'))
    detailShow.value = true
  }
</script>
