<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #content>
      <DrawerTitle>
        <template #default>
          {{ $t('intl.drawer.basicInfo') }}
        </template>
      </DrawerTitle>
      <DrawerGroup>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('intl.form.title')" prop="title">
              {{ form.title }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('intl.form.lang')" prop="lang">
              <dict-tag :options="lang_i18n" :value="form.lang" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('intl.form.code')" prop="code">
              {{ form.code }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('common.createBy')" prop="createBy">
              {{ form.createBy }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('common.createTime')" prop="createTime">
              {{ form.createTime }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('common.updateBy')" prop="updateBy">
              {{ form.updateBy }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('common.updateTime')" prop="updateTime">
              {{ form.updateTime }}
            </el-form-item>
          </el-col>
        </el-row>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, getCurrentInstance, reactive, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { getInternational } from '@/api/system/international/International'

  const { proxy } = getCurrentInstance()

  const { lang_i18n } = proxy.useDict('lang_i18n')
  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    if (props.id) {
      getInternational(props.id).then((response) => {
        data.form = response.data
      })
    }
  }
</script>
