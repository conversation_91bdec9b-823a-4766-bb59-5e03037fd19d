<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    class="upload-dialog"
    title="上传图片"
    width="900px"
    @close="handleDialogClose"
  >
    <el-form :model="form" label-width="100px">
      <el-form-item label="上传方式" required>
        <el-radio-group v-model="form.uploadType">
          <el-radio :label="1">本地上传</el-radio>
          <el-radio :label="2">网络上传</el-radio>
          <el-radio :label="3">扫码上传</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="上传至分组">
        <category-cascader
          v-model="form.groupId"
          :include-all="true"
          clearable
          filterable
          placeholder="请选择分组"
          type="upload_img"
          @change="handleCategoryChange"
        />
      </el-form-item>

      <el-form-item v-if="form.uploadType === 1" label="上传图片">
        <div class="upload-section">
          <div class="upload-area">
            <image-upload
              v-model="form.files"
              :category-id="form.groupId"
              :is-show-tip="false"
              style="width: 360px"
              :showFileList="false"
              @change="handleFileChange"
            />
          </div>

          <div v-if="localUploadedImages.length > 0" class="uploaded-images">
            <div class="uploaded-header">
              <span>已上传 {{ localUploadedImages.length }} 张图片</span>
            </div>
            <div class="image-grid">
              <div v-for="(image, index) in localUploadedImages" :key="index" class="image-item">
                <el-image
                  :initial-index="index"
                  :preview-src-list="localUploadedImages"
                  :src="image"
                  fit="contain"
                />
              </div>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item v-if="form.uploadType === 2" label="网络图片">
        <div class="url-upload">
          <el-input
            v-model="form.url"
            :disabled="uploading"
            placeholder="请输入图片地址"
          ></el-input>
          <el-button :loading="uploading" class="fetch-btn" type="primary" @click="handleFetchImage"
            >提取图片
          </el-button>
        </div>
      </el-form-item>

      <el-form-item v-if="form.uploadType === 3" label="二维码">
        <div class="qrcode-container">
          <div class="qrcode-area">
            <canvas ref="qrcodeCanvas" class="qrcode-canvas"></canvas>
            <div class="qrcode-tip">建议使用手机浏览器</div>
          </div>
          <div v-if="uploadedImages.length > 0" class="uploaded-images">
            <div class="uploaded-header">
              <span>已上传 {{ uploadedImages.length }} 张图片</span>
            </div>
            <div class="image-grid">
              <div v-for="(image, index) in uploadedImages" :key="index" class="image-item">
                <el-image
                  :initial-index="index"
                  :preview-src-list="uploadedImages"
                  :src="image"
                  fit="contain"
                />
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="handleUpload">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { uploadImageFromUrl } from '@/api/system/oss/Oss' // 导入API
  import CategoryCascader from '@/components/CategoryCascader.vue'
  import ImageUpload from '@/components/ImageUpload/index.vue'
  import QRCode from 'qrcode'
  import { computed, defineEmits, getCurrentInstance, nextTick, ref, watch } from 'vue'

  const { proxy } = getCurrentInstance()
  const uploading = ref(false)

  const visible = ref(false)
  const emit = defineEmits(['update:modelValue', 'upload-success'])
  const hasUploaded = ref(false) // 添加标记变量

  const props = defineProps({
    categoryId: {
      type: [String, Number],
      default: ''
    },
    categoryData: {
      type: Array,
      default: () => []
    }
  })

  const form = ref({
    uploadType: 1,
    groupId: String(props.categoryId || '0'),
    files: [],
    imageUrl: ''
  })

  watch(
    () => props.categoryId,
    (newVal) => {
      if (newVal !== undefined && newVal !== null) {
        form.value.groupId = String(newVal)
      }
    },
    { immediate: true }
  )

  const localUploadedImages = ref([])

  const handleFileChange = (files) => {
    form.value.files = files
    localUploadedImages.value = files.map((file) => {
      if (file.url) return file.url
      return URL.createObjectURL(file.raw)
    })
    hasUploaded.value = true // 标记已上传
  }

  const handleUpload = () => {
    // 处理上传逻辑
    visible.value = false
  }

  // 添加处理提取图片的方法
  const handleFetchImage = async () => {
    if (!form.value.url) {
      proxy.$msg.warning('请输入图片地址')
      return
    }

    uploading.value = true
    try {
      let categoryId = form.value.groupId
      await uploadImageFromUrl({
        url: form.value.url,
        categoryId: categoryId === '0' ? undefined : categoryId
      })

      proxy.$msg.success('图片上传成功')
      form.value.url = ''
      hasUploaded.value = true // 标记已上传
      visible.value = false
    } catch (error) {
      console.error('上传图失败:', error)
      proxy.$msg.error('上传失败：' + error.message)
    } finally {
      uploading.value = false
    }
  }

  const qrcodeCanvas = ref(null)
  const qrcodeUrl = ref('')

  // 生成二维码的方法
  const generateQRCode = async () => {
    if (!qrcodeCanvas.value) return

    try {
      // 这里替换成你的实际上传地址
      qrcodeUrl.value = `https://vue.bigtian.club/upload?socketId=${proxy.$socket.id}&categoryId=${form.value.groupId}`
      await QRCode.toCanvas(qrcodeCanvas.value, qrcodeUrl.value, {
        width: 140,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#ffffff'
        }
      })
    } catch (error) {
      console.error('生成二维码失败:', error)
      proxy.$msg.error('生成二维码失败')
    }
  }

  // 监听对话框显示状态，显示时生成二维码
  watch(visible, (newVal) => {
    if (newVal && form.value.uploadType === 3) {
      nextTick(() => {
        generateQRCode()
      })
    }
  })

  // 监听上传方式变化，切换到扫码上传时生成二维码
  watch(
    () => form.value.uploadType,
    (newVal) => {
      if (newVal === 3 && visible.value) {
        nextTick(() => {
          generateQRCode()
        })
      }
    }
  )

  // 刷新二维码
  const refreshQRCode = () => {
    list() // 调用父组件的刷新方法
  }

  // 暴露方法给父组件调用
  defineExpose({
    show: () => {
      visible.value = true
    }
  })

  const uploadedImages = ref([])

  // 处理socket返回的图片接收
  proxy.$socket.on('codeImg', (url) => {
    if (Array.isArray(url)) {
      uploadedImages.value.unshift(...url)
    } else {
      uploadedImages.value.unshift(url)
    }
    hasUploaded.value = true // 标记已上传
  })

  // 移除图片
  const removeImage = (index) => {
    uploadedImages.value.splice(index, 1)
  }

  // 使用上传的图片
  const handleUseImages = () => {
    visible.value = false
  }

  // 添加对话框关闭回调
  const handleDialogClose = () => {
    if (hasUploaded.value) {
      emit('upload-success')
    }
    uploadedImages.value = []
    localUploadedImages.value = []
    form.value.files = []
    hasUploaded.value = false
  }

  const isExpanded = ref(false)

  // 计算是否显示展开按钮
  const showExpandBtn = computed(() => {
    const itemWidth = 100 // 每个图片项的宽度
    const gap = 8 // 间距
    const containerWidth = 800 // 容器宽度(根据实际调整)
    const itemsPerRow = Math.floor((containerWidth + gap) / (itemWidth + gap))
    return uploadedImages.value.length > itemsPerRow * 2
  })

  // 计算要显示的图片
  const displayImages = computed(() => {
    if (!showExpandBtn.value || isExpanded.value) {
      return uploadedImages.value
    }

    const itemsPerRow = Math.floor((800 + 8) / (100 + 8)) // 计算每行可以显示多少张图片
    return uploadedImages.value.slice(0, itemsPerRow * 2) // 只显示两行
  })

  // 监听对话框关闭时重置展开状态
  watch(visible, (newVal) => {
    if (!newVal) {
      isExpanded.value = false
      uploadedImages.value = []
    }
  })

  // 修改样式相关的计算属性
  const gridHeight = computed(() => {
    const itemHeight = 100 // 图片高度
    const gap = 8 // 间距
    const rowHeight = itemHeight + gap
    const itemsPerRow = Math.floor((800 + gap) / (itemHeight + gap))
    const totalRows = Math.ceil(uploadedImages.value.length / itemsPerRow)
    return totalRows * rowHeight
  })

  // 监听分组ID变化，重新生成二维码
  watch(
    () => form.value.groupId,
    (newVal) => {
      if (visible.value && form.value.uploadType === 3) {
        nextTick(() => {
          generateQRCode()
        })
      }
    }
  )

  // 可选：处理分类变化
  const handleCategoryChange = (value) => {
    form.value.groupId = String(value)
    console.log('分类已更改:', value)
  }
</script>

<style scoped>
  .upload-area {
    width: 360px;
  }

  :deep(.el-upload-dragger) {
    width: 360px;
  }

  :deep(.el-upload) {
    width: 360px;
  }

  .url-upload {
    display: flex;
    gap: 12px;
    width: 100%;
    max-width: 600px;
  }

  .fetch-btn {
    flex-shrink: 0;
    min-width: 80px;
    /* 添加最小宽度，防止按钮文字换行 */
  }

  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 8px;
  }

  .qrcode-container {
    display: flex;
    gap: 20px;
    align-items: flex-start;
    width: 100%;
  }

  .qrcode-area {
    flex-shrink: 0;
    width: 140px;
    padding: 8px;
    border: 1px dashed var(--el-border-color);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  .qrcode-canvas {
    width: 140px;
    height: 140px;
  }

  .qrcode-tip {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  :deep(.upload-dialog .el-dialog__body) {
    height: 400px;
    /* 设置一个固定高度，可以根据实际需求调整 */
    overflow-y: auto;
    /* 内容超出时显示滚动条 */
  }

  /* 如果需要调整内容的垂直对齐方式，可以添加 */
  :deep(.upload-dialog .el-form) {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    /* 或者使用 center 使内容垂直居中 */
  }

  /* 修改弹框样式 */
  :deep(.el-dialog) {
    height: 600px;
    /* 设置弹框整体高度 */
  }

  :deep(.el-dialog__body) {
    height: calc(100% - 120px);
    /* 减去头部和底部的高度 */
    padding: 20px;
    overflow-y: auto;
  }

  :deep(.el-dialog__header) {
    padding: 20px;
    margin-right: 0;
  }

  :deep(.el-dialog__footer) {
    padding: 20px;
  }

  /* 表单布局 */
  .el-form {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .qrcode-section {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .uploaded-images {
    flex: 1;
    min-width: 0;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 8px;
    padding: 12px;
    height: 300px;
    display: flex;
    flex-direction: column;
  }

  .uploaded-header {
    margin-bottom: 8px;
    color: var(--el-text-color-secondary);
    font-size: 13px;
    flex-shrink: 0;
  }

  .image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
    overflow-y: auto;
    padding-right: 8px;
    flex: 1;
  }

  .image-item {
    aspect-ratio: 1;
    border-radius: 4px;
    border: 1px solid var(--el-border-color-lighter);
  }

  .image-item .el-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 修改图片展示样式，确保图片完整显示且保持比例 */
  .image-item :deep(.el-image__inner) {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    border-radius: 4px;
  }

  /* 移除之前的内边距和背景色样式 */
  .image-item :deep(.el-image__inner) {
    padding: 0;
    background-color: transparent;
  }

  .image-item:first-child {
    animation: fadeIn 0.3s ease-out;
  }

  /* 优化滚动条样式 */
  .image-grid::-webkit-scrollbar {
    width: 4px;
  }

  .image-grid::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 2px;
  }

  .image-grid::-webkit-scrollbar-track {
    background: var(--el-fill-color-lighter);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 修改对话框样式 */
  :deep(.el-dialog) {
    height: 80vh;
    margin-top: 10vh;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__body) {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
  }

  :deep(.el-dialog__header) {
    padding: 20px;
    margin: 0;
  }

  :deep(.el-dialog__footer) {
    padding: 20px;
    border-top: 1px solid var(--el-border-color-lighter);
  }

  /* 移除之前的固定高度相关样式 */
  :deep(.upload-dialog .el-dialog__body),
  :deep(.upload-dialog .el-form),
  .el-form {
    height: auto;
  }

  .upload-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
  }

  .upload-area {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
</style>
