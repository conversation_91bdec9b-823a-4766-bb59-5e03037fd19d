<template>
  <div class="page-content">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
      <el-form-item :label="$t('name.user')" prop="userName">
        <el-input
          v-model="queryParams.userName"
          :placeholder="$t('name.user.input')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('number.mobile')" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          :placeholder="$t('number.mobile.input')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">{{
          $t('action.search')
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('action.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:add']"
          icon="Plus"
          plain
          type="primary"
          @click="openSelectUser"
          >{{ $t('user.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:remove']"
          :disabled="multiple"
          icon="CircleClose"
          plain
          type="danger"
          @click="cancelAuthUserAll"
          >{{ $t('authorization.cancel.batch') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Close" plain type="warning" @click="handleClose"
          >{{ $t('action.close') }}
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <art-table
      v-loading="loading"
      :data="userList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      row-key="roleId"
      @search="getList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column :label="$t('name.user')" :show-overflow-tooltip="true" prop="userName" />
      <el-table-column :label="$t('nickname.user')" :show-overflow-tooltip="true" prop="nickName" />
      <el-table-column :label="$t('email')" :show-overflow-tooltip="true" prop="email" />
      <el-table-column :label="$t('mobile')" :show-overflow-tooltip="true" prop="phonenumber" />
      <el-table-column :label="$t('status')" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('time.creation')" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['system:role:remove']"
            icon="CircleClose"
            link
            type="primary"
            @click="cancelAuthUser(scope.row)"
          >
            {{ $t('authorization.cancel') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>
    <select-user ref="selectRef" :roleId="queryParams.roleId" @ok="handleQuery" />
  </div>
</template>

<script name="AuthUser" setup>
  import { getCurrentInstance } from 'vue'
  import selectUser from './selectUser'
  import { allocatedUserList, authUserCancel, authUserCancelAll } from '@/api/system/role'

  const { proxy } = getCurrentInstance()

  const route = useRoute()
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const userList = ref([])
  const loading = ref(true)
  const showSearch = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const userIds = ref([])

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 20,
    roleId: route.params.roleId,
    userName: undefined,
    phonenumber: undefined
  })

  /** 查询授权用户列表 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    allocatedUserList(queryParams).then((response) => {
      userList.value = response.rows
      total.value = response.total
      loading.value = false
    })
  }

  /** 返回按钮 */
  function handleClose() {
    const obj = { path: '/system/role' }
    proxy.$tab.closeOpenPage(obj)
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.pageNum = 1
    getList()
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef')
    handleQuery()
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    userIds.value = selection.map((item) => item.userId)
    multiple.value = !selection.length
  }

  /** 打开授权用户表弹窗 */
  function openSelectUser() {
    proxy.$refs['selectRef'].show()
  }

  /** 取消授权按钮操作 */
  function cancelAuthUser(row) {
    proxy.$modal
      .confirm(proxy.$t('user.cancel.confirm') + row.userName + proxy.$t('role.question'))
      .then(function () {
        return authUserCancel({ userId: row.userId, roleId: queryParams.roleId })
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('authorization.cancel.success'))
      })
      .catch(() => {})
  }

  /** 批量取消授权按钮操作 */
  function cancelAuthUserAll(row) {
    const roleId = queryParams.roleId
    const uIds = userIds.value.join(',')
    proxy.$modal
      .confirm(proxy.$t('cancel.user.authorization'))
      .then(function () {
        return authUserCancelAll({ roleId: roleId, userIds: uIds })
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('authorization.cancel.success'))
      })
      .catch(() => {})
  }

  getList()
</script>
