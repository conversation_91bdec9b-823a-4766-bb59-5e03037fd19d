<template>
  <el-form ref="genInfoForm" :model="info" :rules="rules" label-width="150px">
    <el-row>
      <el-col :span="12">
        <el-form-item prop="tplCategory">
          <template #label>{{ $t('template.generate') }}</template>
          <el-select v-model="info.tplCategory" @change="tplSelectChange">
            <el-option :label="$t('table.single.crud')" value="crud" />
            <el-option :label="$t('table.tree.crud')" value="tree" />
            <el-option :label="$t('table.master.detail.crud')" value="sub" />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item prop="tplWebType">
          <template #label>{{ $t('type.front.end') }}</template>
          <el-select v-model="info.tplWebType">
            <el-option :label="$t('template.ui.element.vue2')" value="element-ui" />
            <el-option :label="$t('template.element.plus.vue3')" value="element-plus" />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item prop="packageName">
          <template #label
            >{{ $t('path.package.generation') }}
            <el-tooltip :content="$t('package.java.generate')" placement="top">
              <el-icon>
                <question-filled />
              </el-icon>
            </el-tooltip>
          </template>
          <el-input v-model="info.packageName" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item prop="moduleName">
          <template #label
            >{{ $t('name.module.generate') }}
            <el-tooltip :content="$t('name.subsystem.understood')" placement="top">
              <el-icon>
                <question-filled />
              </el-icon>
            </el-tooltip>
          </template>
          <el-input v-model="info.moduleName" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item prop="businessName">
          <template #label
            >{{ $t('name.business.generate') }}
            <el-tooltip :content="$t('function.name.english')" placement="top">
              <el-icon>
                <question-filled />
              </el-icon>
            </el-tooltip>
          </template>
          <el-input v-model="info.businessName" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item prop="functionName">
          <template #label
            >{{ $t('name.function.generate') }}
            <el-tooltip :content="$t('description.class.used')" placement="top">
              <el-icon>
                <question-filled />
              </el-icon>
            </el-tooltip>
          </template>
          <el-input v-model="info.functionName" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item>
          <template #label>
            <div class="big-flex big-justify-center big-justify-items-center big-align-middle">
              <div>{{ $t('menu.parent') }}</div>
              <el-tooltip :content="$t('menu.specified.assign')" placement="top">
                <el-icon>
                  <question-filled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <tree-select
            v-model:value="info.parentMenuId"
            :objMap="{ value: 'menuId', label: 'menuName', children: 'children' }"
            :options="menuOptions"
            :placeholder="$t('menu.system.select')"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item prop="genType">
          <template #label
            >{{ $t('method.code.generation') }}
            <el-tooltip :content="$t('path.generation.custom')" placement="top">
              <el-icon>
                <question-filled />
              </el-icon>
            </el-tooltip>
          </template>
          <el-radio v-model="info.genType" label="0">{{ $t('package.zip') }}</el-radio>
          <el-radio v-model="info.genType" label="1">{{ $t('path.custom') }}</el-radio>
          <el-radio v-model="info.genType" label="2"
            >{{ $t('directory.specified.generate') }}
          </el-radio>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="generateMenu">
          <template #label>是否生成菜单 </template>
          <el-radio v-model="info.generateMenu" label="0">{{ $t('sys_yes_no.N') }}</el-radio>
          <el-radio v-model="info.generateMenu" label="1">{{ $t('sys_yes_no.Y') }}</el-radio>
        </el-form-item>
      </el-col>

      <el-col v-if="info.genType == '1'" :span="24">
        <el-form-item prop="genPath">
          <template #label
            >{{ $t('path.custom') }}
            <el-tooltip :content="$t('path.disk.absolute')" placement="top">
              <el-icon>
                <question-filled />
              </el-icon>
            </el-tooltip>
          </template>
          <el-input v-model="info.genPath">
            <template #append>
              <el-dropdown>
                <el-button type="primary"
                  >{{ $t('selection.path.quick') }}<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="info.genPath = '/'"
                      >{{ $t('path.generation.base.default') }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col v-if="info.genType == '2'" :span="24">
        <el-form-item prop="controllerPath">
          <template #label>
            <span slot="label"
              >{{ $t('path.controller')
              }}<el-tooltip :content="$t('path.custom.use')" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </span>
          </template>
          <el-input v-model="info.controllerPath" disabled=""></el-input>
        </el-form-item>
        <el-form-item prop="servicePath">
          <template #label>{{ $t('path.service') }}</template>
          <el-input v-model="info.servicePath" disabled></el-input>
        </el-form-item>
        <el-form-item prop="domainPath">
          <template #label>{{ $t('path.domain') }}</template>
          <el-input v-model="info.domainPath" disabled></el-input>
        </el-form-item>
        <el-form-item prop="mapperPath">
          <template #label>{{ $t('path.mapperpath') }}</template>
          <el-input v-model="info.mapperPath" disabled></el-input>
        </el-form-item>
        <el-form-item prop="mapperXmlPath">
          <template #label>{{ $t('path.mapperxml') }}</template>
          <el-input v-model="info.mapperXmlPath" disabled></el-input>
        </el-form-item>
        <el-form-item prop="vuePath">
          <template #label>{{ $t('path.vue') }}</template>
          <el-input v-model="info.vuePath" disabled></el-input>
        </el-form-item>
        <el-form-item prop="apiPath">
          <template #label>{{ $t('path.api') }}</template>
          <el-input v-model="info.apiPath" disabled></el-input>
        </el-form-item>
      </el-col>
    </el-row>

    <template v-if="info.tplCategory == 'tree'">
      <h4 class="form-header">{{ $t('information.other') }}</h4>
      <el-row v-show="info.tplCategory == 'tree'">
        <el-col :span="12">
          <el-form-item>
            <template #label
              >{{ $t('field.encoding.tree') }}
              <el-tooltip :content="$t('tree.display.encoded.field')" placement="top">
                <el-icon>
                  <question-filled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-select v-model="info.treeCode" :placeholder="$t('select.please')">
              <el-option
                v-for="(column, index) in info.columns"
                :key="index"
                :label="column.columnName + '：' + column.columnComment"
                :value="column.columnName"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item>
            <template #label
              >{{ $t('field.encoding.parent.tree') }}
              <el-tooltip :content="$t('field.encoding.parent.tree')" placement="top">
                <el-icon>
                  <question-filled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-select v-model="info.treeParentCode" :placeholder="$t('select.please')">
              <el-option
                v-for="(column, index) in info.columns"
                :key="index"
                :label="column.columnName + '：' + column.columnComment"
                :value="column.columnName"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item>
            <template #label
              >{{ $t('field.name.tree') }}
              <el-tooltip :content="$t('name.field.display.node.tree')" placement="top">
                <el-icon>
                  <question-filled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-select v-model="info.treeName" :placeholder="$t('select.please')">
              <el-option
                v-for="(column, index) in info.columns"
                :key="index"
                :label="column.columnName + '：' + column.columnComment"
                :value="column.columnName"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </template>

    <template v-if="info.tplCategory == 'sub'">
      <h4 class="form-header">{{ $t('information.associated') }}</h4>
      <el-row>
        <el-col :span="12">
          <el-form-item>
            <template #label
              >{{ $t('name.table.sub.associated') }}
              <el-tooltip :content="$t('name.table.sub.associated')" placement="top">
                <el-icon>
                  <question-filled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-select
              v-model="info.subTableName"
              :placeholder="$t('select.please')"
              @change="subSelectChange"
            >
              <el-option
                v-for="(table, index) in tables"
                :key="index"
                :label="table.tableName + '：' + table.tableComment"
                :value="table.tableName"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item>
            <template #label
              >{{ $t('name.key.foreign.associated') }}
              <el-tooltip :content="$t('name.key.foreign.associated')" placement="top">
                <el-icon>
                  <question-filled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-select v-model="info.subTableFkName" :placeholder="$t('select.please')">
              <el-option
                v-for="(column, index) in subColumns"
                :key="index"
                :label="column.columnName + '：' + column.columnComment"
                :value="column.columnName"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </template>
  </el-form>
</template>

<script setup>
  import { getCurrentInstance } from 'vue'
  import { listMenu } from '@/api/system/menu'

  const { proxy } = getCurrentInstance()

  const subColumns = ref([])
  const menuOptions = ref([])

  const props = defineProps({
    info: {
      type: Object,
      default: null
    },
    tables: {
      type: Array,
      default: null
    }
  })

  // 表单校验
  const rules = ref({
    tplCategory: [
      { required: true, message: proxy.$t('template.generation.select'), trigger: 'blur' }
    ],
    packageName: [
      { required: true, message: proxy.$t('path.package.generation.input'), trigger: 'blur' }
    ],
    moduleName: [
      { required: true, message: proxy.$t('name.module.generation.input'), trigger: 'blur' }
    ],
    businessName: [
      { required: true, message: proxy.$t('name.business.generation.input'), trigger: 'blur' }
    ],
    functionName: [
      { required: true, message: proxy.$t('function.name.generated'), trigger: 'blur' }
    ],
    controllerPath: [
      { required: true, message: proxy.$t('path.controller.input'), trigger: 'blur' }
    ],
    servicePath: [{ required: true, message: proxy.$t('service.path.input'), trigger: 'blur' }],
    vuePath: [{ required: true, message: proxy.$t('path.vue.input'), trigger: 'blur' }],
    apiPath: [{ required: true, message: proxy.$t('path.api.input'), trigger: 'blur' }],
    mapperPath: [{ required: true, message: proxy.$t('path.mapper.input'), trigger: 'blur' }],
    mapperXmlPath: [
      { required: true, message: proxy.$t('mapper.xml.path.input'), trigger: 'blur' }
    ],
    domainPath: [{ required: true, message: proxy.$t('path.domain.input'), trigger: 'blur' }]
  })

  function subSelectChange(value) {
    props.info.subTableFkName = ''
  }

  function tplSelectChange(value) {
    if (value !== 'sub') {
      props.info.subTableName = ''
      props.info.subTableFkName = ''
    }
  }

  function setSubTableColumns(value) {
    for (let item in props.tables) {
      const name = props.tables[item].tableName
      if (value === name) {
        subColumns.value = props.tables[item].columns
        break
      }
    }
  }

  /** 查询菜单下拉树结构 */
  function getMenuTreeselect() {
    listMenu().then((response) => {
      menuOptions.value = proxy.handleTree(response.data, 'menuId')
    })
  }

  watch(
    () => props.info.subTableName,
    (val) => {
      setSubTableColumns(val)
    }
  )

  watch(
    () => props.info.tplWebType,
    (val) => {
      if (val === '') {
        props.info.tplWebType = 'element-plus'
      }
    }
  )

  getMenuTreeselect()
</script>
<style scoped>
  .el-icon {
    line-height: unset;
    display: unset;
    padding-left: 2px;
  }
</style>
