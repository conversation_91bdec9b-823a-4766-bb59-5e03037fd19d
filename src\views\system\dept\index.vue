<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="getList"
      @refresh="refresh"
      @search="getList"
    />

    <el-row class="mb8 toolbar-row">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dept:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Sort" plain type="info" @click="toggleExpandAll"
          >{{ $t('expand.collapse') }}
        </el-button>
      </el-col>
      <el-col :span="21">
        <right-toolbar @queryTable="getList" class="right-toolbar"></right-toolbar>
      </el-col>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="deptList"
      :default-expand-all="isExpandAll"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      row-key="deptId"
      class="dept-table"
      border
    >
      <el-table-column :label="$t('name.department')" prop="deptName" min-width="260">
        <template #default="{ row }">
          <el-link type="primary" @click="handleDetail(row)">{{ row.deptName }}</el-link>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('sort')"
        prop="orderNum"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column :label="$t('status')" prop="status" width="100" align="center">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('time.creation')" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('operation')" align="center" width="230px">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:dept:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
          >
            {{ $t('action.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:dept:add']"
            icon="Plus"
            link
            type="primary"
            @click="handleAdd(scope.row)"
          >
            {{ $t('button.add') }}
          </el-button>
          <el-button
            v-if="scope.row.parentId !== 0"
            v-hasPermi="['system:dept:remove']"
            icon="Delete"
            link
            type="danger"
            @click="handleDelete(scope.row)"
            >{{ $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改部门对话框 -->
    <add
      v-model:show="open"
      :title="title"
      :id="deptId"
      :parentId="parentId"
      @refreshList="refreshList"
    />

    <!-- 部门详情对话框 -->
    <detail v-model:show="detailShow" :title="title" :id="deptId" />
  </div>
</template>

<script name="Dept" setup>
  import { delDept, listDept } from '@/api/system/dept'
  import { getCurrentInstance } from 'vue'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import add from './components/add.vue'
  import detail from './components/detail.vue'

  const { proxy } = getCurrentInstance()

  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const deptList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const deptId = ref('')
  const parentId = ref(0)
  const loading = ref(true)
  const title = ref('')
  const isExpandAll = ref(true)
  const refreshTable = ref(true)

  const data = reactive({
    fromKey: 'dept',
    fromName: 'dept',
    queryParams: {
      deptName: undefined,
      status: undefined
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询部门列表 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    listDept(queryParams.value).then((response) => {
      deptList.value = proxy.handleTree(response.data, 'deptId')
      loading.value = false
    })
  }

  /** 新增按钮操作 */
  function handleAdd(row) {
    deptId.value = ''
    parentId.value = row && row.deptId ? row.deptId : 0
    title.value = proxy.$t('department.add')
    open.value = true
  }

  /** 展开/折叠操作 */
  function toggleExpandAll() {
    refreshTable.value = false
    isExpandAll.value = !isExpandAll.value
    nextTick(() => {
      refreshTable.value = true
    })
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    deptId.value = row.deptId
    title.value = proxy.$t('department.modify')
    open.value = true
  }

  /** 查看详情操作 */
  function handleDetail(row) {
    deptId.value = row.deptId
    title.value = proxy.$t('department.detail')
    detailShow.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm(proxy.$t('confirm.delete.name') + row.deptName + proxy.$t('item.data.question'))
      .then(function () {
        return delDept(row.deptId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  const refreshList = () => {
    getList()
  }

  const refresh = () => {
    getList()
  }

  getList()
</script>

<style lang="scss" scoped>
  .page-content {
    padding: 16px;

    .search-form {
      background: #fff;
      padding: 18px;
      border-radius: 4px;
      margin-bottom: 16px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

      .search-buttons {
        margin-right: 0;
      }
    }

    .toolbar-row {
      margin: 16px 0;
      display: flex;
      align-items: center;

      .right-toolbar {
        float: right;
      }
    }

    .dept-table {
      margin-top: 16px;

      :deep(.el-table__header) {
        background-color: #f5f7fa;
      }
    }
  }

  .dept-dialog {
    :deep(.el-dialog__body) {
      padding: 20px 40px;
    }

    .w-full {
      width: 100%;
    }

    .dialog-footer {
      text-align: center;
      padding-top: 20px;
    }
  }

  // 响应式布局调整
  @media screen and (max-width: 768px) {
    .page-content {
      .search-form {
        :deep(.el-form-item) {
          margin-bottom: 10px;
        }
      }
    }
  }
</style>
