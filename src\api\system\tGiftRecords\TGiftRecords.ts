import request from '@/utils/request'

// 查询礼金记录列表
export function listGiftRecords(data) {
  return request({
    url: '/system/giftRecords/list',
    method: 'post',
    data
  })
}

// 查询礼金记录详细
export function getGiftRecords(id) {
  return request({
    url: '/system/giftRecords/' + id,
    method: 'get'
  })
}

// 新增礼金记录
export function addGiftRecords(data) {
  return request({
    url: '/system/giftRecords',
    method: 'post',
    data: data
  })
}

// 修改礼金记录
export function updateGiftRecords(data) {
  return request({
    url: '/system/giftRecords',
    method: 'put',
    data: data
  })
}

// 删除礼金记录
export function delGiftRecords(id) {
  return request({
    url: '/system/giftRecords/' + id,
    method: 'delete'
  })
}
