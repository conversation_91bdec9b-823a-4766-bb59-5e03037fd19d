<template>
  <div>
    <el-select v-model="modelVal" :placeholder="$t(placeholder)" filterable style="width: 100%">
      <el-option
        v-for="dict in options"
        :key="dict.value"
        :label="$t(dict.type + '.' + dict.value)"
        :value="dict.value"
      >
        <el-tag :type="dict.elTagType">
          {{ $t(dict.type + '.' + dict.value) }}
        </el-tag>
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts" setup>
  import { computed, getCurrentInstance } from 'vue'

  let emits = defineEmits(['update:modelValue'])
  const { proxy } = getCurrentInstance()
  let props = defineProps({
    dictKey: {
      required: true,
      type: String
    },
    modelValue: {
      type: String,
      required: true
    },
    placeholder: {
      type: String,
      default: 'select.please'
    }
  })
  const modelVal = computed({
    get: () => props.modelValue,
    set: (val) => {
      emits('update:modelValue', val)
    }
  })
  const options = ref(proxy.useDict(props.dictKey)[props.dictKey])
</script>

<style lang="scss" scoped></style>
