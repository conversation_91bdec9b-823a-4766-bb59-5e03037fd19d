<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="getList"
      @refresh="refresh"
      @search="getList"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:config:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:config:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:config:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:config:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:config:remove']"
          icon="Refresh"
          plain
          type="danger"
          @click="handleRefreshCache"
          >{{ $t('cache.refresh') }}
        </el-button>
      </el-col>
    </el-row>

    <!--<el-table v-loading="loading" :data="configList" @selection-change="handleSelectionChange" height="540px">-->
    <art-table
      v-loading="loading"
      :data="configList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      row-key="configId"
      @search="getList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column :label="$t('key.primary.parameter')" align="center" prop="configId" />
      <el-table-column
        :label="$t('name.parameter')"
        :show-overflow-tooltip="true"
        align="center"
        prop="configName"
      >
        <template #default="{ row }">
          <el-link type="primary" @click="handleDetail(row)">{{ row.configName }}</el-link>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('name.key.parameter')"
        :show-overflow-tooltip="true"
        align="center"
        prop="configKey"
      />
      <el-table-column
        :label="$t('key.value.parameter')"
        :show-overflow-tooltip="true"
        align="center"
        prop="configValue"
      />
      <el-table-column :label="$t('built.in.system')" align="center" prop="configType">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.configType" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('remarks')"
        :show-overflow-tooltip="true"
        align="center"
        prop="remark"
      />
      <el-table-column :label="$t('time.creation')" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
        width="180"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['system:config:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            >{{ $t('action.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:config:remove']"
            icon="Delete"
            link
            type="danger"
            @click="handleDelete(scope.row)"
            >{{ $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改参数配置对话框 -->
    <add v-model:show="open" :title="title" :id="configId" @refreshList="refreshList" />

    <!-- 参数详情对话框 -->
    <detail v-model:show="detailShow" :title="title" :id="configId" />
  </div>
</template>

<script name="Config" setup>
  import { getCurrentInstance } from 'vue'
  import { delConfig, listConfig, refreshCache } from '@/api/system/config'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import add from './components/add.vue'
  import detail from './components/detail.vue'

  const { proxy } = getCurrentInstance()

  const { sys_yes_no } = proxy.useDict('sys_yes_no')

  const configList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const configId = ref('')
  const loading = ref(true)
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')
  const dateRange = ref([])

  const data = reactive({
    fromKey: 'config',
    fromName: 'config',
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      configName: undefined,
      configKey: undefined,
      configType: undefined
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询参数列表 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    listConfig(proxy.addDateRange(queryParams.value, dateRange.value)).then(({ data }) => {
      configList.value = data.records
      total.value = data.totalRow
      loading.value = false
    })
  }



  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.configId)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  /** 新增按钮操作 */
  function handleAdd() {
    configId.value = ''
    title.value = proxy.$t('parameter.add')
    open.value = true
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    configId.value = row.configId || ids.value
    title.value = proxy.$t('parameter.modify')
    open.value = true
  }

  /** 查看详情操作 */
  function handleDetail(row) {
    configId.value = row.configId
    title.value = proxy.$t('parameter.detail')
    detailShow.value = true
  }



  /** 删除按钮操作 */
  function handleDelete(row) {
    const configIds = row.configId || ids.value
    proxy.$modal
      .confirm(
        proxy.$t('confirm.delete.number.parameter') + configIds + proxy.$t('item.data.question')
      )
      .then(function () {
        return delConfig(configIds)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/config/export',
      {
        ...queryParams.value
      },
      `config_${new Date().getTime()}.xlsx`
    )
  }

  /** 刷新缓存按钮操作 */
  function handleRefreshCache() {
    refreshCache().then(() => {
      proxy.$modal.msgSuccess(proxy.$t('cache.refresh.success'))
    })
  }

  const refreshList = () => {
    getList()
  }

  const refresh = () => {
    getList()
  }

  getList()
</script>
