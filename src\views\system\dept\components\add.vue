<template>
  <Drawer v-model="open" @open="init" :size="700">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm">
          {{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="deptRef" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <!-- 上级部门 -->
            <el-col v-if="form.parentId !== 0" :span="24">
              <el-form-item :label="$t('department.parent')" prop="parentId">
                <el-tree-select
                  v-model="form.parentId"
                  :data="deptOptions"
                  :placeholder="$t('department.parent.select')"
                  :props="{ value: 'deptId', label: 'deptName', children: 'children' }"
                  check-strictly
                  value-key="deptId"
                  filterable
                  clearable
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <!-- 部门名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.department')" prop="deptName">
                <el-input v-model="form.deptName" :placeholder="$t('department.name.input')" />
              </el-form-item>
            </el-col>

            <!-- 显示排序 -->
            <el-col :span="12">
              <el-form-item :label="$t('sort.display')" prop="orderNum">
                <el-input-number
                  v-model="form.orderNum"
                  :min="0"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <!-- 负责人 -->
            <el-col :span="12">
              <el-form-item :label="$t('person.charge')" prop="leader">
                <el-input
                  v-model="form.leader"
                  :placeholder="$t('person.charge.input')"
                  maxlength="20"
                />
              </el-form-item>
            </el-col>

            <!-- 联系电话 -->
            <el-col :span="12">
              <el-form-item :label="$t('number.contact')" prop="phone">
                <el-input
                  v-model="form.phone"
                  :placeholder="$t('number.contact.input')"
                  maxlength="11"
                />
              </el-form-item>
            </el-col>

            <!-- 邮箱 -->
            <el-col :span="12">
              <el-form-item :label="$t('email')" prop="email">
                <el-input v-model="form.email" :placeholder="$t('email.input')" maxlength="50" />
              </el-form-item>
            </el-col>

            <!-- 部门状态 -->
            <el-col :span="12">
              <el-form-item :label="$t('status.department')">
                <el-radio-group v-model="form.status">
                  <el-radio
                    v-for="dict in sys_normal_disable"
                    :key="dict.value"
                    :value="dict.value"
                  >
                    {{ $t(dict.type + '.' + dict.value) }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import {
    addDept,
    getDept,
    listDeptExcludeChild,
    listDeptGet,
    updateDept
  } from '@/api/system/dept'

  const { proxy } = getCurrentInstance()
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    },
    parentId: {
      type: [String, Number],
      default: 0
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {},
    rules: {
      parentId: [{ required: true, message: proxy.$t('department.parent.empty'), trigger: 'blur' }],
      deptName: [{ required: true, message: proxy.$t('name.department.empty'), trigger: 'blur' }],
      orderNum: [{ required: true, message: proxy.$t('sort.display.empty'), trigger: 'blur' }],
      email: [
        { type: 'email', message: proxy.$t('address.email.correct'), trigger: ['blur', 'change'] }
      ],
      phone: [
        {
          pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
          message: proxy.$t('number.mobile.correct'),
          trigger: 'blur'
        }
      ]
    },
    deptOptions: []
  })

  const { form, rules, deptOptions } = toRefs(data)

  const loadingBtn = reactive({
    submit: false
  })

  /** 查询部门下拉树结构 */
  function getDeptTreeselect() {
    listDeptGet().then((response) => {
      data.deptOptions = proxy.handleTree(response.data, 'deptId')
    })
  }

  /** 查询部门下拉树结构（排除当前部门及其子部门） */
  function getDeptTreeselectExclude(deptId) {
    listDeptExcludeChild(deptId).then((response) => {
      data.deptOptions = proxy.handleTree(response.data, 'deptId')
    })
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['deptRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true
        if (form.value.deptId !== undefined) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateDept(form.value)
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const add = () => {
    addDept(form.value)
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      // 编辑模式
      getDeptTreeselectExclude(props.id)
      getDept(props.id).then((response) => {
        data.form = response.data
      })
    } else {
      // 新增模式
      getDeptTreeselect()
      if (props.parentId) {
        form.value.parentId = props.parentId
      }
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      deptId: undefined,
      parentId: undefined,
      deptName: undefined,
      orderNum: 0,
      leader: undefined,
      phone: undefined,
      email: undefined,
      status: '0'
    }
    proxy.resetForm('deptRef')
  }
</script>

<style scoped>
  .el-input-number {
    width: 100%;
  }
</style>
