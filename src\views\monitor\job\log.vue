<template>
  <div class="page-content">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
      <el-form-item :label="$t('name.task')" prop="jobName">
        <el-input
          v-model="queryParams.jobName"
          :placeholder="$t('name.task.input')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('name.group.task')" prop="jobGroup">
        <el-select
          v-model="queryParams.jobGroup"
          :placeholder="$t('name.group.task.select')"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in sys_job_group"
            :key="dict.value"
            :label="$t(dict.type + '.' + dict.value)"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('status.execution')" prop="status">
        <el-select
          v-model="queryParams.status"
          :placeholder="$t('status.execution.select')"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in sys_common_status"
            :key="dict.value"
            :label="$t(dict.type + '.' + dict.value)"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('time.execution')" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          :end-placeholder="$t('date.end')"
          :start-placeholder="$t('date.start')"
          range-separator="-"
          type="daterange"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">{{
          $t('action.search')
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('action.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:job:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:job:remove']"
          icon="Delete"
          plain
          type="danger"
          @click="handleClean"
          >{{ $t('action.clear') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:job:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Close" plain type="warning" @click="handleClose"
          >{{ $t('action.close') }}
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="jobLogList"
      height="540px"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column :label="$t('number.log')" align="center" prop="jobLogId" width="80" />
      <el-table-column
        :label="$t('name.task')"
        :show-overflow-tooltip="true"
        align="center"
        prop="jobName"
      />
      <el-table-column
        :label="$t('name.group.task')"
        :show-overflow-tooltip="true"
        align="center"
        prop="jobGroup"
      >
        <template #default="scope">
          <dict-tag :options="sys_job_group" :value="scope.row.jobGroup" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('string.target.call')"
        :show-overflow-tooltip="true"
        align="center"
        prop="invokeTarget"
      />
      <el-table-column
        :label="$t('information.log')"
        :show-overflow-tooltip="true"
        align="center"
        prop="jobMessage"
      />
      <el-table-column :label="$t('status.execution')" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_common_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('time.execution')" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['monitor:job:query']"
            icon="View"
            link
            type="primary"
            @click="handleView(scope.row)"
          >
            {{ $t('details') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNum"
      :total="total"
      @pagination="getList"
    />

    <!-- 调度日志详细 -->
    <el-dialog v-model="open" :title="$t('log.scheduling.details')" append-to-body width="700px">
      <el-form :model="form" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('number.log.serial')">{{ form.jobLogId }}</el-form-item>
            <el-form-item :label="$t('task.name')">{{ form.jobName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('group.task')">{{ form.jobGroup }}</el-form-item>
            <el-form-item :label="$t('time.execution')">{{ form.createTime }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('method.call')">{{ form.invokeTarget }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('log.information')">{{ form.jobMessage }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('status.execution')">
              <div v-if="form.status == 0">{{ $t('status.normal') }}</div>
              <div v-else-if="form.status == 1">{{ $t('status.failed') }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item v-if="form.status == 1" :label="$t('information.exception')"
              >{{ form.exceptionInfo }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="open = false">{{ $t('action.close') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="JobLog" setup>
  import { getCurrentInstance } from 'vue'
  import { getJob } from '@/api/monitor/job'
  import { cleanJobLog, delJobLog, listJobLog } from '@/api/monitor/jobLog'

  const { proxy } = getCurrentInstance()

  const { sys_common_status, sys_job_group } = proxy.useDict('sys_common_status', 'sys_job_group')

  const jobLogList = ref([])
  const open = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const multiple = ref(true)
  const total = ref(0)
  const dateRange = ref([])
  const route = useRoute()

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      dictName: undefined,
      dictType: undefined,
      status: undefined
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  /** 查询调度日志列表 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    listJobLog(proxy.addDateRange(queryParams.value, dateRange.value)).then((response) => {
      jobLogList.value = response.rows
      total.value = response.total
      loading.value = false
    })
  }

  // 返回按钮
  function handleClose() {
    const obj = { path: '/monitor/job' }
    proxy.$tab.closeOpenPage(obj)
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
  }

  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = []
    proxy.resetForm('queryRef')
    handleQuery()
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.jobLogId)
    multiple.value = !selection.length
  }

  /** 详细按钮操作 */
  function handleView(row) {
    open.value = true
    form.value = row
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm(
        proxy.$t('confirm.delete.number.log.scheduling') +
          ids.value +
          proxy.$t('item.data.question')
      )
      .then(function () {
        return delJobLog(ids.value)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 清空按钮操作 */
  function handleClean() {
    proxy.$modal
      .confirm(proxy.$t('confirm.clear.schedule.logs'))
      .then(function () {
        return cleanJobLog()
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('clear.success'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'monitor/jobLog/export',
      {
        ...queryParams.value
      },
      `job_log_${new Date().getTime()}.xlsx`
    )
  }

  ;(() => {
    const jobId = route.params && route.params.jobId
    if (jobId !== undefined && jobId != 0) {
      getJob(jobId).then((response) => {
        queryParams.value.jobName = response.data.jobName
        queryParams.value.jobGroup = response.data.jobGroup
        getList()
      })
    } else {
      getList()
    }
  })()

  getList()
</script>
