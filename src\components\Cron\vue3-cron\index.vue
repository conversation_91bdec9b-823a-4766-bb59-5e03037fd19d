<style lang="scss">
  th {
    text-align: center;
  }

  table {
    width: 100%;
  }

  .tr > td {
    border: 1px solid #e5e5e5;
    text-align: center;
  }

  .vue3-cron-div {
    .el-input-number__decrease,
    .el-input-number__increase {
      top: 2px !important;
    }

    .language {
      position: absolute;
      right: 25px;
      z-index: 1;
    }

    .el-tabs {
      box-shadow: none;
    }

    .tabBody {
      overflow: auto;

      .el-row {
        margin: 20px 0;

        .long {
          .el-select {
            width: 350px;
          }
        }

        .el-input-number {
          width: 110px;
        }
      }
    }

    .myScroller {
      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
      }

      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 10px;
        background-color: skyblue;
        background-image: -webkit-linear-gradient(
          45deg,
          rgba(255, 255, 255, 0.2) 25%,
          transparent 25%,
          transparent 50%,
          rgba(255, 255, 255, 0.2) 50%,
          rgba(255, 255, 255, 0.2) 75%,
          transparent 75%,
          transparent
        );
      }

      &::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: #ededed;
        border-radius: 10px;
      }
    }

    .bottom {
      width: 100%;
      margin-top: 5px;
      display: flex;
      align-items: center;
      justify-content: space-around;

      .value {
        float: left;
        font-size: 14px;
        vertical-align: middle;

        span:nth-child(1) {
          color: red;
        }
      }
    }
  }
</style>
<template>
  <div class="vue3-cron-div">
    <el-tabs type="border-card">
      <el-tab-pane>
        <template #label>
          <span><i class="el-icon-date"></i> {{ state.text.Seconds.name }}</span>
        </template>
        <div :style="{ 'max-height': maxHeight }" class="tabBody myScroller">
          <el-row>
            <el-radio v-model="state.second.cronEvery" label="1"
              >{{ state.text.Seconds.every }}
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.second.cronEvery" label="2"
              >{{ state.text.Seconds.interval[0] }}
              <el-input-number
                v-model="state.second.incrementIncrement"
                :max="60"
                :min="1"
                size="small"
              ></el-input-number>
              {{ state.text.Seconds.interval[1] || '' }}
              <el-input-number
                v-model="state.second.incrementStart"
                :max="59"
                :min="0"
                size="small"
              ></el-input-number>
              {{ state.text.Seconds.interval[2] || '' }}
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.second.cronEvery" class="long" label="3"
              >{{ state.text.Seconds.specific }}
              <el-select v-model="state.second.specificSpecific" multiple size="small">
                <el-option v-for="(val, index) in 60" :key="index" :value="val - 1"
                  >{{ val - 1 }}
                </el-option>
              </el-select>
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.second.cronEvery" label="4"
              >{{ state.text.Seconds.cycle[0] }}
              <el-input-number
                v-model="state.second.rangeStart"
                :max="60"
                :min="1"
                size="small"
              ></el-input-number>
              {{ state.text.Seconds.cycle[1] || '' }}
              <el-input-number
                v-model="state.second.rangeEnd"
                :max="59"
                :min="0"
                size="small"
              ></el-input-number>
              {{ state.text.Seconds.cycle[2] || '' }}
            </el-radio>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane>
        <template #label>
          <span><i class="el-icon-date"></i> {{ state.text.Minutes.name }}</span>
        </template>
        <div :style="{ 'max-height': maxHeight }" class="tabBody myScroller">
          <el-row>
            <el-radio v-model="state.minute.cronEvery" label="1"
              >{{ state.text.Minutes.every }}
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.minute.cronEvery" label="2"
              >{{ state.text.Minutes.interval[0] }}
              <el-input-number
                v-model="state.minute.incrementIncrement"
                :max="60"
                :min="1"
                size="small"
              ></el-input-number>
              {{ state.text.Minutes.interval[1] }}
              <el-input-number
                v-model="state.minute.incrementStart"
                :max="59"
                :min="0"
                size="small"
              ></el-input-number>
              {{ state.text.Minutes.interval[2] || '' }}
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.minute.cronEvery" class="long" label="3"
              >{{ state.text.Minutes.specific }}
              <el-select v-model="state.minute.specificSpecific" multiple size="small">
                <el-option v-for="(val, index) in 60" :key="index" :value="val - 1"
                  >{{ val - 1 }}
                </el-option>
              </el-select>
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.minute.cronEvery" label="4"
              >{{ state.text.Minutes.cycle[0] }}
              <el-input-number
                v-model="state.minute.rangeStart"
                :max="60"
                :min="1"
                size="small"
              ></el-input-number>
              {{ state.text.Minutes.cycle[1] }}
              <el-input-number
                v-model="state.minute.rangeEnd"
                :max="59"
                :min="0"
                size="small"
              ></el-input-number>
              {{ state.text.Minutes.cycle[2] }}
            </el-radio>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane>
        <template #label>
          <span><i class="el-icon-date"></i> {{ state.text.Hours.name }}</span>
        </template>
        <div :style="{ 'max-height': maxHeight }" class="tabBody myScroller">
          <el-row>
            <el-radio v-model="state.hour.cronEvery" label="1"
              >{{ state.text.Hours.every }}
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.hour.cronEvery" label="2"
              >{{ state.text.Hours.interval[0] }}
              <el-input-number
                v-model="state.hour.incrementIncrement"
                :max="23"
                :min="0"
                size="small"
              ></el-input-number>
              {{ state.text.Hours.interval[1] }}
              <el-input-number
                v-model="state.hour.incrementStart"
                :max="23"
                :min="0"
                size="small"
              ></el-input-number>
              {{ state.text.Hours.interval[2] }}
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.hour.cronEvery" class="long" label="3"
              >{{ state.text.Hours.specific }}
              <el-select v-model="state.hour.specificSpecific" multiple size="small">
                <el-option v-for="(val, index) in 24" :key="index" :value="val - 1"
                  >{{ val - 1 }}
                </el-option>
              </el-select>
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.hour.cronEvery" label="4"
              >{{ state.text.Hours.cycle[0] }}
              <el-input-number
                v-model="state.hour.rangeStart"
                :max="23"
                :min="0"
                size="small"
              ></el-input-number>
              {{ state.text.Hours.cycle[1] }}
              <el-input-number
                v-model="state.hour.rangeEnd"
                :max="23"
                :min="0"
                size="small"
              ></el-input-number>
              {{ state.text.Hours.cycle[2] }}
            </el-radio>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane>
        <template #label>
          <span><i class="el-icon-date"></i> {{ state.text.Day.name }}</span>
        </template>
        <div :style="{ 'max-height': maxHeight }" class="tabBody myScroller">
          <el-row>
            <el-radio v-model="state.day.cronEvery" label="1">{{ state.text.Day.every }} </el-radio>
          </el-row>

          <el-row>
            <el-radio v-model="state.day.cronEvery" label="3"
              >{{ state.text.Day.intervalDay[0] }}
              <el-input-number
                v-model="state.day.incrementIncrement"
                :max="31"
                :min="1"
                size="small"
              ></el-input-number>
              {{ state.text.Day.intervalDay[1] }}
              <el-input-number
                v-model="state.day.incrementStart"
                :max="31"
                :min="1"
                size="small"
              ></el-input-number>
              {{ state.text.Day.intervalDay[2] }}
            </el-radio>
          </el-row>

          <el-row>
            <el-radio v-model="state.day.cronEvery" class="long" label="5"
              >{{ state.text.Day.specificDay }}
              <el-select v-model="state.day.specificSpecific" multiple size="small">
                <el-option v-for="(val, index) in 31" :key="index" :value="val"
                  >{{ val }}
                </el-option>
              </el-select>
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.day.cronEvery" label="6"
              >{{ state.text.Day.lastDay }}
            </el-radio>
          </el-row>

          <el-row>
            <el-radio v-model="state.day.cronEvery" label="9">
              <el-input-number
                v-model="state.day.cronDaysBeforeEomMinus"
                :max="31"
                :min="1"
                size="small"
              ></el-input-number>
              {{ state.text.Day.beforeEndMonth[0] }}
            </el-radio>
          </el-row>

          <el-row>
            <el-radio v-model="state.day.cronEvery" label="7"
              >{{ state.text.Week.lastWeekday }}
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.day.cronEvery" label="8"
              >{{ state.text.Week.lastWeek[0] }}
              <el-select v-model="state.day.cronLastSpecificDomDay" size="small">
                <el-option
                  v-for="(val, index) in 7"
                  :key="index"
                  :label="state.text.Week.list[val - 1]"
                  :value="val"
                ></el-option>
              </el-select>
              {{ state.text.Week.lastWeek[1] || '' }}
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.day.cronEvery" label="10"
              >{{ state.text.Week.nearestWeekday[0] }}
              <el-input-number
                v-model="state.day.cronDaysNearestWeekday"
                :max="31"
                :min="1"
                size="small"
              ></el-input-number>
              {{ state.text.Week.nearestWeekday[1] }}
            </el-radio>
          </el-row>
        </div>
      </el-tab-pane>
      <!--      周-->
      <el-tab-pane>
        <template #label>
          <span><i class="el-icon-date"></i> {{ state.text.Week.name }}</span>
        </template>
        <div :style="{ 'max-height': maxHeight }" class="tabBody myScroller">
          <el-row>
            <el-radio v-model="state.day.cronEvery" label="12"
              >{{ state.text.Week.every }}
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.day.cronEvery" label="2"
              >{{ state.text.Week.intervalWeek[0] }}
              <el-input-number
                v-model="state.week.incrementIncrement"
                :max="7"
                :min="1"
                size="small"
              ></el-input-number>
              {{ state.text.Week.intervalWeek[1] }}
              <el-select v-model="state.week.incrementStart" size="small">
                <el-option
                  v-for="(val, index) in 7"
                  :key="index"
                  :label="state.text.Week.list[val - 1]"
                  :value="val"
                ></el-option>
              </el-select>
              {{ state.text.Week.intervalWeek[2] }}
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.day.cronEvery" class="long" label="4"
              >{{ state.text.Week.specificWeek }}
              <el-select v-model="state.week.specificSpecific" multiple size="small">
                <el-option
                  v-for="(val, index) in 7"
                  :key="index"
                  :label="state.text.Week.list[val - 1]"
                  :value="['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'][val - 1]"
                ></el-option>
              </el-select>
            </el-radio>
          </el-row>

          <el-row>
            <el-radio v-model="state.day.cronEvery" label="11"
              >{{ state.text.Week.someWeekday[0] }}
              <el-input-number
                v-model="state.week.cronNthDayNth"
                :max="5"
                :min="1"
                size="small"
              ></el-input-number>
              <el-select v-model="state.week.cronNthDayDay" size="small">
                <el-option
                  v-for="(val, index) in 7"
                  :key="index"
                  :label="state.text.Week.list[val - 1]"
                  :value="val"
                ></el-option>
              </el-select>
              {{ state.text.Week.someWeekday[1] }}
            </el-radio>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane>
        <template #label>
          <span><i class="el-icon-date"></i> {{ state.text.Month.name }}</span>
        </template>
        <div :style="{ 'max-height': maxHeight }" class="tabBody myScroller">
          <el-row>
            <el-radio v-model="state.month.cronEvery" label="1"
              >{{ state.text.Month.every }}
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.month.cronEvery" label="2"
              >{{ state.text.Month.interval[0] }}
              <el-input-number
                v-model="state.month.incrementIncrement"
                :max="12"
                :min="0"
                size="small"
              ></el-input-number>
              {{ state.text.Month.interval[1] }}
              <el-input-number
                v-model="state.month.incrementStart"
                :max="12"
                :min="0"
                size="small"
              ></el-input-number>
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.month.cronEvery" class="long" label="3"
              >{{ state.text.Month.specific }}
              <el-select v-model="state.month.specificSpecific" multiple size="small">
                <el-option
                  v-for="(val, index) in 12"
                  :key="index"
                  :label="val"
                  :value="val"
                ></el-option>
              </el-select>
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.month.cronEvery" label="4"
              >{{ state.text.Month.cycle[0] }}
              <el-input-number
                v-model="state.month.rangeStart"
                :max="12"
                :min="1"
                size="small"
              ></el-input-number>
              {{ state.text.Month.cycle[1] }}
              <el-input-number
                v-model="state.month.rangeEnd"
                :max="12"
                :min="1"
                size="small"
              ></el-input-number>
            </el-radio>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane>
        <template #label>
          <span><i class="el-icon-date"></i> {{ state.text.Year.name }}</span>
        </template>
        <div :style="{ 'max-height': maxHeight }" class="tabBody myScroller">
          <el-row>
            <el-radio v-model="state.year.cronEvery" label="1"
              >{{ state.text.Year.every }}
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.year.cronEvery" label="2"
              >{{ state.text.Year.interval[0] }}
              <el-input-number
                v-model="state.year.incrementIncrement"
                :max="99"
                :min="1"
                size="small"
              ></el-input-number>
              {{ state.text.Year.interval[1] }}
              <el-input-number
                v-model="state.year.incrementStart"
                :max="state.currentYear + 10"
                :min="state.currentYear - 1"
                size="small"
              ></el-input-number>
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.year.cronEvery" class="long" label="3"
              >{{ state.text.Year.specific }}
              <el-select v-model="state.year.specificSpecific" filterable multiple size="small">
                <el-option
                  v-for="(val, index) in 20"
                  :key="index"
                  :label="state.currentYear + index"
                  :value="state.currentYear + index"
                ></el-option>
              </el-select>
            </el-radio>
          </el-row>
          <el-row>
            <el-radio v-model="state.year.cronEvery" label="4"
              >{{ state.text.Year.cycle[0] }}
              <el-input-number
                v-model="state.year.rangeStart"
                :max="state.currentYear + 20"
                :min="state.currentYear"
                size="small"
              ></el-input-number>
              {{ state.text.Year.cycle[1] }}
              <el-input-number
                v-model="state.year.rangeEnd"
                :max="state.currentYear + 20"
                :min="state.currentYear"
                size="small"
              ></el-input-number>
            </el-radio>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>
    <div>
      <div>
        <el-divider content-position="left">{{ $t('expression.time') }}</el-divider>
        <table>
          <thead>
            <th v-for="item of tabTitles" :key="item" width="40">{{ item }}</th>
            <th>{{ $t('expression.cron') }}</th>
          </thead>
          <tbody>
            <tr class="tr">
              <td>
                <span>{{ state.secondsText }}</span>
              </td>
              <td>
                <span>{{ state.minutesText }}</span>
              </td>
              <td>
                <span>{{ state.hoursText }}</span>
              </td>
              <td>
                <span>{{ state.daysText }}</span>
              </td>
              <td>
                <span>{{ state.monthsText }}</span>
              </td>
              <td>
                <span>{{ state.weeksText }}</span>
              </td>
              <td>
                <span>{{ state.yearsText }}</span>
              </td>
              <td>
                <span>{{ state.cron }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div>
        <el-divider content-position="left">{{ $t('times.running.last.5') }}</el-divider>
        <p v-for="item in resultList" style="margin: 0">{{ item }}</p>
      </div>
      <div class="buttonDiv" style="width: 100%; text-align: center">
        <el-button size="mini" type="primary" @click.stop="handleChange">{{
          state.text.Save
        }}</el-button>
        <el-button size="mini" type="danger" @click="close">{{ state.text.Close }}</el-button>
        <el-button size="mini" type="warning" @click="resetCron">{{ state.text.Reset }}</el-button>
      </div>
    </div>
  </div>
</template>
<script>
  import Language from './language'
  import { computed, defineComponent, onMounted, reactive, ref, toRefs, watch } from 'vue'
  import { getExecuteTime } from '@/api/job/index'

  export default defineComponent({
    name: 'vue3Cron',
    props: {
      cronValue: {},
      i18n: {},
      maxHeight: {},
      cron: ''
    },
    setup(props, { emit }) {
      const { i18n } = toRefs(props)
      const resultList = ref([])
      const tabTitles = ref([
        this.$t('unit.seconds'),
        this.$t('unit.minute'),
        this.$t('unit.hour'),
        this.$t('unit.day'),
        this.$t('unit.month'),
        this.$t('unit.week'),
        this.$t('unit.year')
      ])

      const state = reactive({
        language: i18n.value,
        currentYear: new Date().getFullYear(),
        second: {
          cronEvery: '1',
          incrementStart: 3,
          incrementIncrement: 5,
          rangeStart: 0,
          rangeEnd: 0,
          specificSpecific: []
        },
        minute: {
          cronEvery: '1',
          incrementStart: 3,
          incrementIncrement: 5,
          rangeStart: 0,
          rangeEnd: 0,
          specificSpecific: []
        },
        hour: {
          cronEvery: '1',
          incrementStart: 3,
          incrementIncrement: 5,
          rangeStart: 0,
          rangeEnd: 0,
          specificSpecific: []
        },
        day: {
          cronEvery: '1',
          incrementStart: 1,
          incrementIncrement: 1,
          rangeStart: 0,
          rangeEnd: 0,
          specificSpecific: [],
          cronLastSpecificDomDay: 1,
          cronDaysBeforeEomMinus: 0,
          cronDaysNearestWeekday: 0
        },
        week: {
          cronEvery: '1',
          incrementStart: 1,
          incrementIncrement: 1,
          specificSpecific: [],
          cronNthDayDay: 1,
          cronNthDayNth: 1
        },
        month: {
          cronEvery: '1',
          incrementStart: 3,
          incrementIncrement: 5,
          rangeStart: 0,
          rangeEnd: 0,
          specificSpecific: []
        },
        year: {
          cronEvery: '1',
          incrementStart: 2022,
          incrementIncrement: 1,
          rangeStart: 0,
          rangeEnd: 0,
          specificSpecific: []
        },
        output: {
          second: '',
          minute: '',
          hour: '',
          day: '',
          month: '',
          Week: '',
          year: ''
        },
        text: computed(() => Language[state.language || 'cn']),
        secondsText: computed(() => {
          let seconds = ''
          let cronEvery = state.second.cronEvery
          switch (cronEvery.toString()) {
            case '1':
              seconds = '*'
              break
            case '2':
              seconds = state.second.incrementStart + '/' + state.second.incrementIncrement
              break
            case '3':
              state.second.specificSpecific.map((val) => {
                seconds += val + ','
              })
              seconds = seconds.slice(0, -1) || '*'
              break
            case '4':
              seconds = state.second.rangeStart + '-' + state.second.rangeEnd
              break
          }
          return seconds
        }),
        minutesText: computed(() => {
          let minutes = ''
          let cronEvery = state.minute.cronEvery
          switch (cronEvery.toString()) {
            case '1':
              minutes = '*'
              break
            case '2':
              minutes = state.minute.incrementStart + '/' + state.minute.incrementIncrement
              break
            case '3':
              state.minute.specificSpecific.map((val) => {
                minutes += val + ','
              })
              minutes = minutes.slice(0, -1) || '*'
              break
            case '4':
              minutes = state.minute.rangeStart + '-' + state.minute.rangeEnd
              break
          }
          return minutes
        }),
        hoursText: computed(() => {
          let hours = ''
          let cronEvery = state.hour.cronEvery
          switch (cronEvery.toString()) {
            case '1':
              hours = '*'
              break
            case '2':
              hours = state.hour.incrementStart + '/' + state.hour.incrementIncrement
              break
            case '3':
              state.hour.specificSpecific.map((val) => {
                hours += val + ','
              })
              hours = hours.slice(0, -1) || '*'
              break
            case '4':
              hours = state.hour.rangeStart + '-' + state.hour.rangeEnd
              break
          }
          return hours
        }),
        daysText: computed(() => {
          let days = ''
          let cronEvery = state.day.cronEvery
          switch (cronEvery.toString()) {
            case '1':
              days = '*'
              break
            case '2':
            case '4':
            case '11':
              days = '?'
              break
            case '3':
              days = state.day.incrementStart + '/' + state.day.incrementIncrement
              break
            case '5':
              state.day.specificSpecific.map((val) => {
                days += val + ','
              })
              days = days.slice(0, -1) || '*'
              break
            case '6':
              days = 'L'
              break
            case '7':
              days = 'LW'
              break
            case '8':
              days = state.day.cronLastSpecificDomDay + 'L'
              break
            case '9':
              days = 'L-' + state.day.cronDaysBeforeEomMinus
              break
            case '10':
              days = state.day.cronDaysNearestWeekday + 'W'
              break
            case '12':
              days = '?'
              break
          }
          return days
        }),
        weeksText: computed(() => {
          let weeks = ''
          let cronEvery = state.day.cronEvery
          switch (cronEvery.toString()) {
            case '1':
            case '3':
            case '5':
            case '6':
            case '7':
            case '8':
            case '9':
            case '10':
              weeks = '?'
              break
            case '2':
              weeks = state.week.incrementStart + '/' + state.week.incrementIncrement
              break
            case '4':
              state.week.specificSpecific.map((val) => {
                weeks += val + ','
              })
              weeks = weeks.slice(0, -1) || '*'
              break
            case '11':
              weeks = state.week.cronNthDayDay + '#' + state.week.cronNthDayNth
              break
            case '12':
              weeks = '*'
              break
          }
          return weeks
        }),
        monthsText: computed(() => {
          let months = ''
          let cronEvery = state.month.cronEvery
          switch (cronEvery.toString()) {
            case '1':
              months = '*'
              break
            case '2':
              months = state.month.incrementStart + '/' + state.month.incrementIncrement
              break
            case '3':
              state.month.specificSpecific.map((val) => {
                months += val + ','
              })
              months = months.slice(0, -1) || '*'
              break
            case '4':
              months = state.month.rangeStart + '-' + state.month.rangeEnd
              break
          }
          return months
        }),
        yearsText: computed(() => {
          let years = ''
          let cronEvery = state.year.cronEvery
          switch (cronEvery.toString()) {
            case '1':
              years = '*'
              break
            case '2':
              years = state.year.incrementStart + '/' + state.year.incrementIncrement
              break
            case '3':
              state.year.specificSpecific.map((val) => {
                years += val + ','
              })
              years = years.slice(0, -1) || '*'
              break
            case '4':
              years = state.year.rangeStart + '-' + state.year.rangeEnd
              break
          }
          return years
        }),
        cron: computed(() => {
          return `${state.secondsText || '*'} ${
            state.minutesText || '*'
          } ${state.hoursText || '*'} ${state.daysText || '*'} ${state.monthsText || '*'} ${
            state.weeksText || '?'
          } ${state.yearsText || '*'}`
        })
      })
      watch(() => {
        getExecuteTime({
          cron: state.cron,
          count: 5
        }).then((res) => {
          resultList.value = res.data
        })
      })

      onMounted(() => {
        analysisCron()
      })
      //反解析cron
      const analysisCron = () => {
        let cron = props.cron
        if (cron === '' || cron === undefined) {
          return
        }
        const cronArr = cron.split(' ')
        analysisSecond(cronArr[0])
        analysisMinute(cronArr[1])
        analysisHour(cronArr[2])
        analysisDay(cronArr[3])
        analysisWeek(cronArr[5])
        analysisMonth(cronArr[4])
        analysisYear(cronArr[6])
      }
      //解析秒
      const analysisSecond = (val) => {
        setData(state.second, val)
      }
      //解析分
      const analysisMinute = (val) => {
        setData(state.minute, val)
      }
      //解析小时
      const analysisHour = (val) => {
        setData(state.hour, val)
      }
      //解析日
      const analysisDay = (val) => {
        if (val === '?') {
          return
        }
        let obj = state.day
        if (val === '*') {
          obj.cronEvery = '1'
          return
        }
        //每隔
        if (val.indexOf('/') !== -1) {
          obj.cronEvery = '3'
          let values = val.split('/')
          obj.incrementStart = parseInt(values[0])
          obj.incrementIncrement = parseInt(values[1])
          return
        }
        //  最后一天
        if ('L' === val) {
          obj.cronEvery = '6'
          return
        }
        //本月底前
        if (val.startsWith('L-')) {
          obj.cronEvery = '9'
          obj.cronDaysBeforeEomMinus = parseInt(val.split('-')[1])

          return
        }
        //  最后工作日
        if (val === 'LW') {
          obj.cronEvery = '7'
          return
        }
        //这个月的最后星期几
        if (val.endsWith('L')) {
          obj.cronEvery = '8'
          obj.cronLastSpecificDomDay = parseInt(val.split(0, 1))
          return
        }
        // 最近的工作日
        if (val.endsWith('W')) {
          obj.cronEvery = '10'
          obj.cronDaysNearestWeekday = parseInt(val.split(0, 1))
          return
        }
        //  具体天数
        obj.cronEvery = '5'
        obj.specificSpecific = val.split(',')
      }
      //解析月
      const analysisMonth = (val) => {
        setData(state.month, val)
      }
      //解析周
      const analysisWeek = (val) => {
        if (val === '?') {
          return
        }

        let obj = state.week

        if (val === '*') {
          state.day.cronEvery = '12'
          return
        }
        if (val.indexOf('#') !== -1) {
          state.day.cronEvery = '11'
          let split = val.split('#')
          obj.cronNthDayDay = split[0]
          obj.cronNthDayNth = parseInt(split[1])
          return
        }
        //每隔
        if (val.indexOf('/') !== -1) {
          state.day.cronEvery = '2'
          let values = val.split('/')
          obj.incrementStart = parseInt(values[0])
          obj.incrementIncrement = parseInt(values[1])
          return
        }

        state.day.cronEvery = '4'

        obj.specificSpecific = val.split(',')
        // setData(obj, val)
      }
      //解析年
      const analysisYear = (val) => {
        setData(state.year, val === undefined ? '*' : val)
      }

      const setData = (obj, val) => {
        if (typeof val === undefined) {
          return
        }
        val = val + ''
        if (val === '*') {
          obj.cronEvery = '1'
          return
        }

        //每隔
        if (val.indexOf('/') !== -1) {
          obj.cronEvery = '2'
          var values = val.split('/')
          obj.incrementStart = parseInt(values[0])
          obj.incrementIncrement = parseInt(values[1])
          return
        }
        //  周期
        if (val.indexOf('-') !== -1) {
          obj.cronEvery = '4'
          var values = val.split('-')

          obj.rangeStart = parseInt(values[0])
          obj.rangeEnd = parseInt(values[1])
          return
        }
        obj.cronEvery = '3'

        obj.specificSpecific = val.split(',')
      }

      const getValue = () => {
        return state.cron
      }
      const close = () => {
        resetCron()
        emit('close')
      }
      const handleChange = () => {
        emit('change', state.cron)
        resetCron()
        close()
      }
      //重置
      const resetCron = () => {
        state.second.cronEvery = '1'
        state.minute.cronEvery = '1'
        state.hour.cronEvery = '1'
        state.day.cronEvery = '1'
        state.week.cronEvery = '1'
        state.month.cronEvery = '1'
        state.year.cronEvery = '1'

        emit('reset', state)
      }
      const rest = (data) => {
        for (let i in data) {
          if (data[i] instanceof Object) {
            this.rest(data[i])
          } else {
            switch (typeof data[i]) {
              case 'object':
                data[i] = []
                break
              case 'string':
                data[i] = ''
                break
            }
          }
        }
      }
      return {
        tabTitles,
        resultList,
        state,
        analysisCron,
        getValue,
        close,
        handleChange,
        resetCron,
        rest
      }
    }
  })
</script>
