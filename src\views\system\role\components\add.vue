<template>
  <Drawer v-model="open" @open="init" :size="700">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm">
          {{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="roleRef" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <!-- 角色名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.role')" prop="roleName">
                <el-input v-model="form.roleName" :placeholder="$t('name.role.input')" />
              </el-form-item>
            </el-col>
            <!-- 权限字符 -->
            <el-col :span="12">
              <el-form-item prop="roleKey">
                <template #label>
                  <span>{{ $t('character.permission') }}</span>
                </template>
                <el-input v-model="form.roleKey" :placeholder="$t('character.permission.input')" />
              </el-form-item>
            </el-col>

            <!-- 角色顺序 -->
            <el-col :span="12">
              <el-form-item :label="$t('sequence.role')" prop="roleSort">
                <el-input-number v-model="form.roleSort" :min="0" controls-position="right" />
              </el-form-item>
            </el-col>
            <!-- 状态 -->
            <el-col :span="12">
              <el-form-item :label="$t('status')">
                <el-radio-group v-model="form.status">
                  <el-radio
                    v-for="dict in sys_normal_disable"
                    :key="dict.value"
                    :value="dict.value"
                  >
                    {{ $t(dict.type + '.' + dict.value) }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <!-- 菜单权限 -->
            <el-col :span="24">
              <el-form-item :label="$t('permission.menu')">
                <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">
                  {{ $t('expand.collapse') }}
                </el-checkbox>
                <el-checkbox
                  v-model="menuNodeAll"
                  @change="handleCheckedTreeNodeAll($event, 'menu')"
                >
                  {{ $t('select.all.none') }}
                </el-checkbox>
                <el-checkbox
                  v-model="form.menuCheckStrictly"
                  @change="handleCheckedTreeConnect($event, 'menu')"
                >
                  {{ $t('linkage.parent.child') }}
                </el-checkbox>
                <el-tree
                  ref="menuRef"
                  :check-strictly="!form.menuCheckStrictly"
                  :data="menuOptions"
                  :empty-text="$t('loading')"
                  :props="{ label: 'label', children: 'children' }"
                  class="tree-border"
                  node-key="id"
                  show-checkbox
                ></el-tree>
              </el-form-item>
            </el-col>

            <!-- 备注 -->
            <el-col :span="24">
              <el-form-item :label="$t('remarks')">
                <el-input
                  v-model="form.remark"
                  :placeholder="$t('content.input')"
                  type="textarea"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs, nextTick } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { addRole, getRole, updateRole } from '@/api/system/role'
  import { roleMenuTreeselect, treeselect as menuTreeselect } from '@/api/system/menu'

  const { proxy } = getCurrentInstance()
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {},
    rules: {
      roleName: [{ required: true, message: proxy.$t('name.role.empty'), trigger: 'blur' }],
      roleKey: [
        { required: true, message: proxy.$t('character.permission.empty'), trigger: 'blur' }
      ],
      roleSort: [{ required: true, message: proxy.$t('sequence.role.empty'), trigger: 'blur' }]
    },
    menuOptions: [],
    menuExpand: false,
    menuNodeAll: false
  })

  const { form, rules, menuOptions, menuExpand, menuNodeAll } = toRefs(data)
  const menuRef = ref(null)

  const loadingBtn = reactive({
    submit: false
  })

  /** 查询菜单树结构 */
  function getMenuTreeselect() {
    menuTreeselect().then((response) => {
      let menuData = response.data
      setI18nTitle(menuData)
      data.menuOptions = menuData
    })
  }

  const setI18nTitle = (el) => {
    el.forEach((el) => {
      el.label = proxy.$t(el.label)
      if (el.children?.length > 0) {
        setI18nTitle(el.children)
      }
    })
  }

  /** 根据角色ID查询菜单树结构 */
  function getRoleMenuTreeselect(roleId) {
    return roleMenuTreeselect(roleId).then((response) => {
      data.menuOptions = response.menus
      return response
    })
  }

  /** 树权限（展开/折叠）*/
  function handleCheckedTreeExpand(value, type) {
    if (type === 'menu') {
      let treeList = data.menuOptions
      for (let i = 0; i < treeList.length; i++) {
        menuRef.value.store.nodesMap[treeList[i].id].expanded = value
      }
    }
  }

  /** 树权限（全选/全不选） */
  function handleCheckedTreeNodeAll(value, type) {
    if (type === 'menu') {
      menuRef.value.setCheckedNodes(value ? data.menuOptions : [])
    }
  }

  /** 树权限（父子联动） */
  function handleCheckedTreeConnect(value, type) {
    if (type === 'menu') {
      form.value.menuCheckStrictly = value ? true : false
    }
  }

  /** 所有菜单节点数据 */
  function getMenuAllCheckedKeys() {
    // 目前被选中的菜单节点
    let checkedKeys = menuRef.value.getCheckedKeys()
    // 半选中的菜单节点
    let halfCheckedKeys = menuRef.value.getHalfCheckedKeys()
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)
    return checkedKeys
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['roleRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true
        if (form.value.roleId != undefined) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    form.value.menuIds = getMenuAllCheckedKeys()
    updateRole(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const add = () => {
    form.value.menuIds = getMenuAllCheckedKeys()
    addRole(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      const roleMenu = getRoleMenuTreeselect(props.id)
      getRole(props.id).then((response) => {
        data.form = response.data
        data.form.roleSort = Number(data.form.roleSort)
        nextTick(() => {
          roleMenu.then((res) => {
            let checkedKeys = res.checkedKeys
            checkedKeys.forEach((v) => {
              nextTick(() => {
                menuRef.value.setChecked(v, true, false)
              })
            })
          })
        })
      })
    } else {
      getMenuTreeselect()
    }
  }

  // 表单重置
  function reset() {
    if (menuRef.value != undefined) {
      menuRef.value.setCheckedKeys([])
    }
    data.menuExpand = false
    data.menuNodeAll = false
    form.value = {
      roleId: undefined,
      roleName: undefined,
      roleKey: undefined,
      roleSort: 0,
      status: '0',
      menuIds: [],
      menuCheckStrictly: true,
      remark: undefined
    }
    proxy.resetForm('roleRef')
  }
</script>

<style scoped>
  .tree-border {
    margin-top: 10px;
    border: 1px solid var(--el-border-color-light);
    background: var(--el-bg-color);
    border-radius: 4px;
    width: 100%;
    padding: 10px;
    min-height: 200px;
  }

  :deep(.el-tree-node__content) {
    height: 32px;
    line-height: 32px;
  }

  :deep(.el-tree-node__content:hover) {
    background-color: var(--el-color-primary-light-9);
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }

  :deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }
</style>
