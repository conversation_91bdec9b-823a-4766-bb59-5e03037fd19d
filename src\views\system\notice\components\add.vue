<template>
  <Drawer v-model="open" @open="init" :size="1600">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm">
          {{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="noticeRef" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <!-- 公告标题 -->
            <el-col :span="12">
              <el-form-item :label="$t('title.notice')" prop="noticeTitle">
                <el-input v-model="form.noticeTitle" :placeholder="$t('title.notice.input')" />
              </el-form-item>
            </el-col>

            <!-- 公告类型 -->
            <el-col :span="12">
              <el-form-item :label="$t('type.notice')" prop="noticeType">
                <el-select
                  v-model="form.noticeType"
                  :placeholder="$t('select.please')"
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in sys_notice_type"
                    :key="dict.value"
                    :label="$t(dict.type + '.' + dict.value)"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 状态 -->
            <el-col :span="24">
              <el-form-item :label="$t('status')">
                <el-radio-group v-model="form.status">
                  <el-radio v-for="dict in sys_notice_status" :key="dict.value" :value="dict.value">
                    {{ $t(dict.type + '.' + dict.value) }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <!-- 内容 -->
            <el-col :span="24">
              <el-form-item :label="$t('content')">
                <editor v-model="form.noticeContent" v-if="open" :min-height="300" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import editor from '@/components/core/forms/art-wang-editor/index.vue'
  import { addNotice, getNotice, updateNotice } from '@/api/system/notice'

  const { proxy } = getCurrentInstance()
  const { sys_notice_status, sys_notice_type } = proxy.useDict(
    'sys_notice_status',
    'sys_notice_type'
  )

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {},
    rules: {
      noticeTitle: [{ required: true, message: proxy.$t('title.notice.empty'), trigger: 'blur' }],
      noticeType: [{ required: true, message: proxy.$t('type.notice.empty'), trigger: 'change' }]
    }
  })

  const { form, rules } = toRefs(data)

  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['noticeRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true
        if (form.value.noticeId !== undefined) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateNotice(form.value)
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const add = () => {
    addNotice(form.value)
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getNotice(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      noticeId: undefined,
      noticeTitle: undefined,
      noticeType: undefined,
      noticeContent: undefined,
      status: '0'
    }
    proxy.resetForm('noticeRef')
  }
</script>

<style scoped>
  /* 组件样式 */
</style>
