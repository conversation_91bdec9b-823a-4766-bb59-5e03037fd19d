<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="giftBooksRef" :model="form" :rules="rules">
          <el-row>
            <!--礼簿标题-->
            <el-col :span="12">
              <el-form-item :label="$t('giftBooks.title')" prop="title">
                <el-input
                  v-model="form.title"
                  :placeholder="formatStr($t('input.please'), $t('giftBooks.title'))"
                />
              </el-form-item>
            </el-col>

            <!--礼簿类型(字典：gift_book_type)-->
            <el-col :span="12"> </el-col>

            <!--用户ID-->
            <el-col :span="12">
              <el-form-item :label="$t('giftBooks.userId')" prop="userId">
                <el-input
                  v-model="form.userId"
                  :placeholder="formatStr($t('input.please'), $t('giftBooks.userId'))"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'
  import { computed, toRefs } from 'vue'
  import { addGiftBooks, getGiftBooks, updateGiftBooks } from '@/api/system/tGiftBooks/TGiftBooks'

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {},
    rules: {
      title: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('giftBooks.title'), proxy.$t('not.empty')),
          trigger: ['blur', 'change']
        }
      ],
      type: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('giftBooks.type'), proxy.$t('not.empty')),
          trigger: 'change'
        }
      ],
      userId: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('giftBooks.userId'), proxy.$t('not.empty')),
          trigger: ['blur', 'change']
        }
      ]
    }
  })

  const { form, rules } = toRefs(data)
  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['giftBooksRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true

        if (form.value.id != null) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateGiftBooks(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }
  const add = () => {
    addGiftBooks(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))

        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getGiftBooks(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      title: null,
      type: null,
      userId: null
    }
    proxy.resetForm('giftBooksRef')
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }
</script>
