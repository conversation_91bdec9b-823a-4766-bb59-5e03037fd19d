<template>
  <div class="page-content">
    <high-query
      @refresh="refresh"
      @search="search"
      @load="search"
      :formKey="data.fromKey"
      :formName="data.fromName"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" @click="newClick()" icon="Plus" plain>
          {{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:weddingPhotos:edit']"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:weddingPhotos:remove']"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:weddingPhotos:export']"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
    </el-row>

    <art-table
      v-loading="loading"
      :data="weddingPhotosList"
      v-model:page-num="queryParams.pageNum"
      @selection-change="handleSelectionChange"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="search"
    >
      <el-table-column type="selection" width="55" />
      <!--${comment}-->
      <!--类型-->
      <el-table-column :label="$t('weddingPhotos.type')" :show-overflow-tooltip="true" prop="type">
        <template #default="scope">
          <dict-tag :options="resource_type" :value="scope.row.type" />
        </template>
      </el-table-column>
      <!--链接-->
      <el-table-column :label="$t('weddingPhotos.url')" :show-overflow-tooltip="true" prop="url">
        <template #default="scope">
          <image-preview :src="scope.row.url" :width="50" :height="50" />
        </template>
      </el-table-column>
      <!--描述-->
      <!--<el-table-column-->
      <!--  :label="$t('weddingPhotos.desc')"-->
      <!--  prop="desc"-->
      <!--  :show-overflow-tooltip="true"-->
      <!--/>-->
      <!--排序-->
      <el-table-column
        :label="$t('weddingPhotos.sortOrder')"
        prop="sortOrder"
        :show-overflow-tooltip="true"
      />
      <el-table-column :label="$t('table.operation')" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            icon="Edit"
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:weddingPhotos:edit']"
            >{{ $t('action.modify') }}
          </el-button>
          <el-button
            link
            icon="Delete"
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:weddingPhotos:remove']"
            >{{ $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改婚纱照对话框 -->

    <add v-model:show="open" :title="title" :id="id" @refreshList="refreshList" />
    <!-- 详情婚纱照对话框 -->
    <detail v-model:show="detailShow" :title="title" :id="id" />
    <picture-list v-model:show="dialogVisible" :max-select="9999" @select="handleImageSelect" />
  </div>
</template>

<script setup name="WeddingPhotos">
  import {
    addBatch,
    delWeddingPhotos,
    listWeddingPhotos
  } from '@/api/system/weddingPhotos/WeddingPhotos'
  import Add from './components/add.vue'
  import Detail from './components/detail.vue'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'

  const { proxy } = getCurrentInstance()
  const { resource_type } = proxy.useDict('resource_type')

  const weddingPhotosList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const loading = ref(true)
  const dialogVisible = ref(false)

  const ids = ref([])
  const id = ref()
  const names = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')
  const type = ref('')

  const data = reactive({
    fromKey: 'l_wedding_photos',
    fromName: '婚纱照',
    deptList: [],
    userList: [],

    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20
    }
  })
  const newClick = () => {
    type.value = 'index-banner'
    dialogVisible.value = true
    // if (e !== 'music') {
    //   dialogVisible.value = true
    // } else {
    //   handleAdd()
    // }
  }

  const { queryParams} = toRefs(data)

  function refresh(query) {
    query.pageNum = 1
    search(query)
  }


  function search(query) {
    if (query) {
      data.queryParams = { ...data.queryParams, ...query }
    }
    loading.value = true
    listWeddingPhotos(data.queryParams)
      .then(({ data }) => {
        weddingPhotosList.value = data.records
        total.value = data.totalRow
        loading.value = false
      })
  }


  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id)
    names.value = selection.map((item) => item.type)

    single.value = selection.length != 1
    multiple.value = !selection.length
  }


  /** 修改按钮操作 */
  function handleUpdate(row) {
    id.value = row.id || ids.value
    title.value = proxy.formatStr(proxy.$t('action.modify') + ' ' + proxy.$t('menu.weddingPhotos'))
    open.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const idKeys = row.id || ids.value
    const nameArr = row.type || names.value
    proxy.$modal
      .confirm(proxy.$t('confirm.delete', { name: nameArr }))
      .then(function () {
        return delWeddingPhotos(idKeys)
      })
      .then(() => {
        search()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/weddingPhotos/export',
      {
        ...queryParams.value
      },
      `weddingPhotos_${new Date().getTime()}.xlsx`
    )
  }

  const refreshList = (pageNum) => {
    if (pageNum) {
      data.queryParams.pageNum = 1
    }
    search()
  }
  const handleImageSelect = (val) => {
    dialogVisible.value = false
    let sort = 0
    val.forEach((item) => {
      item.type = type.value
      item.sortOrder = sort++
    })
    addBatch(val).then(() => {
      proxy.$msg.success('保存成功')
      search()
    })
  }
</script>
