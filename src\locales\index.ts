import type { I18n, I18nOptions } from 'vue-i18n'
import { createI18n } from 'vue-i18n'
import { LanguageEnum } from '@/enums/appEnum'
import { getSystemStorage } from '@/utils/storage'
import { StorageKeyManager } from '@/utils/storage/storage-key-manager'

// 创建存储键管理器实例
const storageKeyManager = new StorageKeyManager()

// 语言选项
export const languageOptions = [
  { value: LanguageEnum.ZH, label: '简体中文' },
  { value: LanguageEnum.EN, label: 'English' }
]

/**
 * 从存储中获取语言设置
 * @returns 语言设置，如果获取失败则返回默认语言
 */
const getDefaultLanguage = (): LanguageEnum => {
  // 尝试从版本化的存储中获取语言设置
  try {
    const storageKey = storageKeyManager.getStorageKey('user')
    const userStore = localStorage.getItem(storageKey)

    if (userStore) {
      const { language } = JSON.parse(userStore)
      if (language && Object.values(LanguageEnum).includes(language)) {
        return language
      }
    }
  } catch (error) {
    console.warn('[i18n] 从版本化存储获取语言设置失败:', error)
  }

  // 尝试从系统存储中获取语言设置
  try {
    const sys = getSystemStorage()
    if (sys) {
      const { user } = JSON.parse(sys)
      if (user?.language && Object.values(LanguageEnum).includes(user.language)) {
        return user.language
      }
    }
  } catch (error) {
    console.warn('[i18n] 从系统存储获取语言设置失败:', error)
  }

  // 返回默认语言
  console.debug('[i18n] 使用默认语言:', LanguageEnum.ZH)
  return LanguageEnum.ZH
}

// 创建 i18n 配置对象
const i18nOptions: I18nOptions = {
  locale: getDefaultLanguage(), // 设置当前语言
  legacy: false,
  globalInjection: true,
  fallbackLocale: LanguageEnum.ZH, // 备用语言
  messages: {} // 默认的消息对象为空，将由异步获取的内容填充
}

const i18n: I18n = createI18n(i18nOptions)

// 调用初始化方法，加载语言数据

// 提供用于获取翻译内容的 getLang 方法
export const getLang = (msg: string) => {
  if (!i18n) {
    return ''
  }
  return i18n.global.t(msg)
}

interface Translation {
  (key: string): string
}

export const $t = i18n.global.t as Translation

export default i18n
