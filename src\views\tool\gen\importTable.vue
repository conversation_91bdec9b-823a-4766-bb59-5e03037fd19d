<template>
  <!-- 导入表 -->
  <el-dialog v-model="visible" :title="$t('table.import')" append-to-body top="5vh" width="800px">
    <el-form ref="queryRef" :inline="true" :model="queryParams">
      <el-form-item :label="$t('name.table')" prop="tableName">
        <el-input
          v-model="queryParams.tableName"
          :placeholder="$t('name.table.input')"
          clearable
          style="width: 180px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('description.table')" prop="tableComment">
        <el-input
          v-model="queryParams.tableComment"
          :placeholder="$t('description.table.input')"
          clearable
          style="width: 180px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">{{
          $t('action.search')
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('action.reset') }}</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table
        ref="table"
        :data="dbTableList"
        height="540px"
        @row-click="clickRow"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column
          :label="$t('name.table')"
          :show-overflow-tooltip="true"
          prop="tableName"
        ></el-table-column>
        <el-table-column
          :label="$t('description.table')"
          :show-overflow-tooltip="true"
          prop="tableComment"
        ></el-table-column>
        <el-table-column :label="$t('time.creation')" prop="createTime"></el-table-column>
        <el-table-column :label="$t('time.update')" prop="updateTime"></el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNum"
        :total="total"
        @pagination="getList"
      />
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleImportTable">{{ $t('action.confirm') }}</el-button>
        <el-button @click="visible = false">{{ $t('common.cancel') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getCurrentInstance } from 'vue'
  import { importTable, listDbTable } from '@/api/tool/gen'

  const { proxy } = getCurrentInstance()

  const total = ref(0)
  const visible = ref(false)
  const tables = ref([])
  const dbTableList = ref([])

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 20,
    tableName: undefined,
    tableComment: undefined
  })

  const emit = defineEmits(['ok'])

  /** 查询参数列表 */
  function show() {
    getList()
    visible.value = true
  }

  /** 单击选择行 */
  function clickRow(row) {
    proxy.$refs.table.toggleRowSelection(row)
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    tables.value = selection.map((item) => item.tableName)
  }

  /** 查询表数据 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    listDbTable(queryParams).then((res) => {
      dbTableList.value = res.rows
      total.value = res.total
    })
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.pageNum = 1
    getList()
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef')
    handleQuery()
  }

  /** 导入按钮操作 */
  function handleImportTable() {
    const tableNames = tables.value.join(',')
    if (tableNames == '') {
      proxy.$modal.msgError(proxy.$t('table.import.select'))
      return
    }
    importTable({ tables: tableNames }).then((res) => {
      proxy.$modal.msgSuccess(res.msg)
      if (res.code === 200) {
        visible.value = false
        emit('ok')
      }
    })
  }

  defineExpose({
    show
  })
</script>
