<template>
  <div class="page-content">
    <el-row :gutter="20">
      <!--用户数据-->
      <el-col :span="24" :xs="24">
        <high-query
          :formKey="data.fromKey"
          :formName="data.fromName"
          @load="search"
          @refresh="refresh"
          @search="search"
        />

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:user:add']"
              icon="Plus"
              plain
              type="primary"
              @click="handleAdd"
              >{{ $t('action.add.new') }}
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:user:edit']"
              :disabled="single"
              icon="Edit"
              plain
              type="success"
              @click="handleUpdate"
              >{{ $t('action.modify') }}
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:user:remove']"
              :disabled="multiple"
              icon="Delete"
              plain
              type="danger"
              @click="handleDelete"
              >{{ $t('action.delete') }}
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:user:import']"
              icon="Upload"
              plain
              type="info"
              @click="handleImport"
              >{{ $t('action.import') }}
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:user:export']"
              icon="Download"
              plain
              type="warning"
              @click="handleExport"
              >{{ $t('action.export') }}
            </el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            :columns="columns"
            @queryTable="search"
          ></right-toolbar>
        </el-row>

        <!--<el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange" height="540px">-->
        <art-table
          v-loading="loading"
          :data="userList"
          v-model:page-num="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          row-key="userId"
          @search="search"
          @selection-change="handleSelectionChange"
        >
          <el-table-column align="center" type="selection" width="50" />
          <el-table-column
            v-if="columns[0].visible"
            key="userId"
            :label="$t('number.user')"
            align="center"
            prop="userId"
          />
          <el-table-column
            v-if="columns[1].visible"
            key="userName"
            :label="$t('name.user')"
            :show-overflow-tooltip="true"
            align="center"
            prop="userName"
          />
          <el-table-column
            v-if="columns[2].visible"
            key="nickName"
            :label="$t('nickname.user')"
            :show-overflow-tooltip="true"
            align="center"
            prop="nickName"
          >
            <template #default="{ row }">
              <el-link type="primary" @click="handleDetail(row)">{{ row.nickName }}</el-link>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns[3].visible"
            key="deptName"
            :label="$t('department')"
            :show-overflow-tooltip="true"
            align="center"
            prop="dept.deptName"
          />
          <el-table-column
            v-if="columns[4].visible"
            key="phonenumber"
            :label="$t('number.mobile')"
            align="center"
            prop="phonenumber"
            width="120"
          />
          <el-table-column
            v-if="columns[5].visible"
            key="status"
            :label="$t('status')"
            align="center"
          >
            <template #default="scope">
              <el-switch
                v-model="scope.row.status"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns[6].visible"
            :label="$t('time.creation')"
            align="center"
            prop="createTime"
            width="160"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('operation')"
            align="center"
            class-name="small-padding fixed-width"
            width="180"
          >
            <template #default="scope">
              <el-tooltip
                v-if="scope.row.userId !== 1"
                :content="$t('action.modify')"
                placement="top"
              >
                <el-button
                  v-hasPermi="['system:user:edit']"
                  icon="Edit"
                  link
                  type="primary"
                  @click="handleUpdate(scope.row)"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                v-if="scope.row.userId !== 1"
                :content="$t('action.delete')"
                placement="top"
              >
                <el-button
                  v-hasPermi="['system:user:remove']"
                  icon="Delete"
                  link
                  type="danger"
                  @click="handleDelete(scope.row)"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                v-if="scope.row.userId !== 1"
                :content="$t('password.reset')"
                placement="top"
              >
                <el-button
                  v-hasPermi="['system:user:resetPwd']"
                  icon="Key"
                  link
                  type="primary"
                  @click="handleResetPwd(scope.row)"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                v-if="scope.row.userId !== 1"
                :content="$t('role.assign')"
                placement="top"
              >
                <el-button
                  v-hasPermi="['system:user:edit']"
                  icon="CircleCheck"
                  link
                  type="primary"
                  @click="handleAuthRole(scope.row)"
                ></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </art-table>
      </el-col>
    </el-row>

    <!-- 添加或修改用户对话框 -->
    <add v-model:show="open" :title="title" :id="userId" @refreshList="refreshList" />

    <!-- 用户详情对话框 -->
    <detail v-model:show="detailShow" :title="title" :id="userId" />

    <!-- 用户导入对话框 -->
    <el-dialog v-model="upload.open" :title="upload.title" append-to-body width="400px">
      <el-upload
        ref="uploadRef"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :auto-upload="false"
        :disabled="upload.isUploading"
        :headers="upload.headers"
        :limit="1"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        accept=".xlsx, .xls"
        drag
      >
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text"
          >{{ $t('files.drag.here') }}<em>{{ $t('upload.click') }}</em></div
        >
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport">
                {{ $t('data.user.existing.update') }}
              </el-checkbox>
            </div>
            <span>{{ $t('import.files.format') }}</span>
            <el-link
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              type="primary"
              @click="importTemplate"
              >{{ $t('template.download') }}
            </el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">{{ $t('action.confirm') }}</el-button>
          <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="User" setup>
  import { getCurrentInstance } from 'vue'
  import { getToken } from '@/utils/auth'
  import add from './components/add.vue'
  import detail from './components/detail.vue'
  import {
    changeUserStatus,
    delUser,
    deptTreeSelect,
    listUser,
    resetUserPwd
  } from '@/api/system/user'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'

  const { proxy } = getCurrentInstance()

  const router = useRouter()
  const { sys_normal_disable, sys_user_sex } = proxy.useDict('sys_normal_disable', 'sys_user_sex')

  const userList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const userId = ref('')
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')
  const deptOptions = ref(undefined)
  /*** 用户导入参数 */
  const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: '',
    // 是否禁用上传
    isUploading: false,
    // 是否更新已经存在的用户数据
    updateSupport: 0,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_API_URL + '/system/user/importData'
  })
  // 列显隐信息
  const columns = ref([
    { key: 0, label: proxy.$t('number.user'), visible: true },
    { key: 1, label: proxy.$t('name.user'), visible: true },
    { key: 2, label: proxy.$t('nickname.user'), visible: true },
    { key: 3, label: proxy.$t('department'), visible: true },
    { key: 4, label: proxy.$t('number.mobile'), visible: true },
    { key: 5, label: proxy.$t('status'), visible: true },
    { key: 6, label: proxy.$t('time.creation'), visible: true }
  ])

  const data = reactive({
    fromKey: 'user',
    fromName: 'user',
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      userName: undefined,
      phonenumber: undefined,
      status: undefined,
      deptId: undefined
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询部门下拉树结构 */
  function getDeptTree() {
    deptTreeSelect().then((response) => {
      deptOptions.value = response.data
    })
  }
  function refresh(query) {
    query.pageNum = 1
    search(query)
  }

  function search(query) {
    if (query) {
      data.queryParams = { ...data.queryParams, ...query }
    }
    loading.value = true
    listUser(data.queryParams)
      .then(({ data }) => {
        userList.value = data.records
        total.value = data.totalRow
        loading.value = false
      })
      .catch((err) => {
        loading.value = false
      })
  }



  /** 删除按钮操作 */
  function handleDelete(row) {
    const userIds = row.userId || ids.value
    proxy.$modal
      .confirm(proxy.$t('confirm.delete.user.id') + userIds + proxy.$t('item.data.question'))
      .then(function () {
        return delUser(userIds)
      })
      .then(() => {
        search()
        proxy.$modal.msgSuccess(proxy.$t('delete.success'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/user/export',
      {
        ...queryParams.value
      },
      `user_${new Date().getTime()}.xlsx`
    )
  }

  /** 用户状态修改  */
  function handleStatusChange(row) {
    let text = row.status === '0' ? proxy.$t('action.enable') : proxy.$t('action.disable')
    proxy.$modal
      .confirm(proxy.$t('confirm.action') + text + '""' + row.userName + proxy.$t('user.question'))
      .then(function () {
        return changeUserStatus(row.userId, row.status)
      })
      .then(() => {
        proxy.$modal.msgSuccess(text + proxy.$t('status.success'))
      })
      .catch(function () {
        row.status = row.status === '0' ? '1' : '0'
      })
  }

  /** 更多操作 */
  function handleCommand(command, row) {
    switch (command) {
      case 'handleResetPwd':
        handleResetPwd(row)
        break
      case 'handleAuthRole':
        handleAuthRole(row)
        break
      default:
        break
    }
  }

  /** 跳转角色分配 */
  function handleAuthRole(row) {
    const userId = row.userId
    router.push('/system/user-auth/role/' + userId)
  }

  /** 重置密码按钮操作 */
  function handleResetPwd(row) {
    proxy.$modal
      .prompt(
        proxy.$t('input.please') + `【${row.userName}】` + proxy.$t('password.new'),
        proxy.$t('tip'),
        {
          confirmButtonText: proxy.$t('action.confirm'),
          cancelButtonText: proxy.$t('action.cancel'),
          closeOnClickModal: false,
          inputPattern: /^.{5,20}$/,
          inputErrorMessage: proxy.$t('length.password.user'),
          inputValidator: (value) => {
            if (/<|>|"|'|\||\\/.test(value)) {
              return proxy.$t('characters.illegal')
            }
          }
        }
      )
      .then(({ value }) => {
        resetUserPwd(row.userId, value).then((response) => {
          proxy.$modal.msgSuccess(proxy.$t('password.new.modified') + value)
        })
      })
      .catch(() => {})
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.userId)
    single.value = selection.length !== 1
    multiple.value = selection.length === 0
  }

  /** 导入按钮操作 */
  function handleImport() {
    upload.title = proxy.$t('user.import')
    upload.open = true
  }

  /** 下载模板操作 */
  function importTemplate() {
    proxy.download('system/user/importTemplate', {}, `user_template_${new Date().getTime()}.xlsx`)
  }

  /**文件上传中处理 */
  const handleFileUploadProgress = (event, file, fileList) => {
    upload.isUploading = true
  }

  /** 文件上传成功处理 */
  const handleFileSuccess = (response, file, fileList) => {
    upload.open = false
    upload.isUploading = false
    proxy.$refs['uploadRef'].handleRemove(file)
    proxy.$alert(
      "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        '</div>',
      proxy.$t('results.import'),
      { dangerouslyUseHTMLString: true }
    )
    search()
  }

  /** 提交上传文件 */
  function submitFileForm() {
    proxy.$refs['uploadRef'].submit()
  }



  /** 新增按钮操作 */
  function handleAdd() {
    userId.value = ''
    title.value = proxy.$t('user.add')
    open.value = true
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    userId.value = row.userId || ids.value
    title.value = proxy.$t('user.modify')
    open.value = true
  }

  /** 查看详情操作 */
  function handleDetail(row) {
    userId.value = row.userId
    title.value = proxy.$t('user.detail')
    detailShow.value = true
  }


  const refreshList = (pageNum) => {
    if (pageNum) {
      data.queryParams.pageNum = 1
    }
    search()
  }
  getDeptTree()
</script>
