<template>
  <div class="product-specs">
    <el-row>
      <el-col>
        <el-form-item label="规格类型" prop="specType">
          <el-radio-group v-model="form.specType">
            <el-radio v-for="dict in spec_type" :key="dict.value" :label="dict.value"
              >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
    <div v-if="form.specType === 'multi_gauge'" class="big-mt-5">
      <div class="specs-header">
        <span>规格</span>
        <span>{{ specList.length }}/10</span>
        <el-button :disabled="specList.length >= 10" link type="primary" @click="addSpec">
          添加规格组
        </el-button>
      </div>
      <TransitionGroup name="list">
        <draggable
          v-model="specList"
          animation="300"
          handle=".drag-handle"
          item-key="now"
          @end="drag = false"
          @start="drag = true"
        >
          <template #item="{ element, index }">
            <div class="spec-item">
              <div class="spec-header">
                <el-icon class="drag-handle">
                  <Grid />
                </el-icon>
                <el-input
                  v-model="element.specName"
                  class="spec-name-input"
                  placeholder="规格名称"
                />
                <el-button link type="danger" @click="removeSpec(index)"> 删除</el-button>
              </div>

              <div class="spec-values">
                <TransitionGroup class="tag-container" name="tag" tag="div">
                  <draggable
                    v-model="element.specValues"
                    :item-key="(item) => item"
                    animation="300"
                    @end="dragValue = false"
                    @start="dragValue = true"
                  >
                    <template #item="{ element: value, index: valueIndex }">
                      <el-tag
                        class="spec-value-tag"
                        closable
                        @close="removeValue(index, valueIndex)"
                      >
                        {{ value }}
                      </el-tag>
                    </template>
                  </draggable>
                </TransitionGroup>

                <el-input
                  v-if="element.inputVisible"
                  ref="inputRef"
                  v-model="element.inputValue"
                  class="value-input"
                  size="small"
                  @blur="confirmValue(index)"
                  @keyup.enter="confirmValue(index)"
                />

                <el-button v-else class="button-new-tag" size="small" @click="showInput(index)">
                  添加规格值
                </el-button>
              </div>
            </div>
          </template>
        </draggable>
      </TransitionGroup>
    </div>

    <div v-else>
      <el-row>
        <!--原价-->
        <el-col :span="12">
          <el-form-item :label="$t('products.originalPrice')" prop="originalPrice">
            <el-input
              v-model="form.originalPrice"
              :placeholder="formatStr($t('input.please'), $t('products.originalPrice'))"
            />
          </el-form-item>
        </el-col>

        <!--现价/销售价-->
        <el-col :span="12">
          <el-form-item :label="$t('products.sellingPrice')" prop="sellingPrice">
            <el-input
              v-model="form.sellingPrice"
              :placeholder="formatStr($t('input.please'), $t('products.sellingPrice'))"
            />
          </el-form-item>
        </el-col>
        <!--成本价-->
        <el-col :span="12">
          <el-form-item label="成本价" prop="sellingPrice">
            <el-input
              v-model="form.sellingPrice"
              :placeholder="formatStr($t('input.please'), $t('products.sellingPrice'))"
            />
          </el-form-item>
        </el-col>
        <!--总库存-->
        <el-col :span="12">
          <el-form-item :label="$t('products.totalStock')" prop="totalStock">
            <el-input
              v-model="form.totalStock"
              :placeholder="formatStr($t('input.please'), $t('products.totalStock'))"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, nextTick, ref } from 'vue'
  import draggable from 'vuedraggable'
  import { formatStr } from '@/utils/ruoyi'

  const { proxy } = getCurrentInstance()
  const { spec_type } = proxy.useDict('spec_type')

  const props = defineProps({
    product: {
      type: Object,
      default: () => ({})
    },
    modelValue: {
      type: Array,
      default: () => []
    }
  })

  const form = computed({
    get: () => {
      let product = props.product
      if (!product.specType) {
        product.specType = 'single_gauge'
      }
      return product
    },
    set: (val) => emit('update:product', val)
  })
  const emit = defineEmits(['update:modelValue', 'update:product'])

  const specList = ref(props.modelValue)
  const inputRef = ref()
  // 监听规格列表变化
  watch(
    specList,
    (newVal) => {
      emit('update:modelValue', newVal)
    },
    { deep: true }
  )

  // 添加规格组
  const addSpec = () => {
    specList.value.push({
      now: Date.now(),
      specName: '',
      specValues: [],
      inputVisible: false,
      inputValue: ''
    })
  }

  // 删除规格组
  const removeSpec = (index) => {
    specList.value.splice(index, 1)
  }

  // 显示输入框
  const showInput = async (index) => {
    specList.value[index].inputVisible = true
    await nextTick()
    inputRef.value?.focus()
  }

  // 确认添加规格值
  const confirmValue = (index) => {
    const spec = specList.value[index]
    if (spec.inputValue) {
      let items = spec.inputValue.trim()
      spec.inputValue = ''
      if (spec.specValues.includes(items)) {
        proxy.$msg.error('规格值重复')
        return
      }
      spec.specValues.push(items)
    }
    spec.inputVisible = false
  }

  // 删除规格值
  const removeValue = (specIndex, valueIndex) => {
    specList.value[specIndex].specValues.splice(valueIndex, 1)
  }

  // 添加规格值拖拽状态
  const dragValue = ref(false)
</script>

<style lang="scss" scoped>
  .product-specs {
    padding: 20px;
    border-radius: 8px;
    background: var(--el-bg-color);

    .specs-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      span:nth-child(2) {
        color: var(--el-text-color-secondary);
        margin: 0 10px;
      }
    }

    .spec-item {
      padding: 16px;
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      margin-bottom: 10px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      .spec-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .drag-handle {
          cursor: move;
          margin-right: 10px;
          color: var(--el-text-color-secondary);
        }

        .spec-name-input {
          width: 200px;
          margin-right: auto;
        }
      }

      .spec-values {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .tag-container {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        :deep(.sortable-drag) {
          opacity: 0;
        }

        :deep(.sortable-ghost) {
          opacity: 0.5;
          background: var(--el-color-primary-light-9);
        }

        :deep(.sortable-chosen) {
          background: var(--el-color-primary-light-9);
        }

        .spec-value-tag {
          margin-right: 8px;
          cursor: move;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
          }
        }

        // 添加 tag 移动动画
        .tag-move {
          transition: all 0.5s ease;
        }

        .tag-enter-active,
        .tag-leave-active {
          transition: all 0.3s ease;
          position: absolute;
        }

        .tag-enter-from,
        .tag-leave-to {
          opacity: 0;
          transform: scale(0.8);
        }

        .value-input {
          width: 100px;
          transition: all 0.3s ease;
        }
      }
    }

    // 规格组的过渡动画
    .list-enter-active,
    .list-leave-active {
      transition: all 0.3s ease;
    }

    .list-enter-from,
    .list-leave-to {
      opacity: 0;
      transform: translateX(30px);
    }
  }
</style>
