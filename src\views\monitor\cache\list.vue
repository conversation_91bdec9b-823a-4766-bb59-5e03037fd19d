<template>
  <div class="">
    <el-row :gutter="10">
      <el-col :span="8">
        <el-card style="height: calc(100vh - 125px)">
          <template #header>
            <Collection style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('list.cache') }}</span>
            <el-button
              icon="Refresh"
              link
              style="float: right; padding: 3px 0"
              type="primary"
              @click="refreshCacheNames()"
            ></el-button>
          </template>
          <el-table
            v-loading="loading"
            :data="cacheNames"
            :height="tableHeight"
            highlight-current-row
            style="width: 100%"
            @row-click="getCacheKeys"
          >
            <el-table-column
              :label="$t('number.serial')"
              type="index"
              width="120"
            ></el-table-column>

            <el-table-column
              :formatter="nameFormatter"
              :label="$t('name.cache')"
              :show-overflow-tooltip="true"
              align="center"
              prop="cacheName"
              width="100"
            ></el-table-column>

            <el-table-column
              :label="$t('remarks')"
              :show-overflow-tooltip="true"
              align="center"
              prop="remark"
            />
            <el-table-column
              :label="$t('operation')"
              align="center"
              class-name="small-padding fixed-width"
              width="100"
            >
              <template #default="scope">
                <el-button
                  icon="Delete"
                  link
                  type="danger"
                  @click="handleClearCacheName(scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card style="height: calc(100vh - 125px)">
          <template #header>
            <Key style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('list.key.name') }}</span>
            <el-button
              icon="Refresh"
              link
              style="float: right; padding: 3px 0"
              type="primary"
              @click="refreshCacheKeys()"
            ></el-button>
          </template>
          <el-table
            v-loading="subLoading"
            :data="cacheKeys"
            :height="tableHeight"
            highlight-current-row
            style="width: 100%"
            @row-click="handleCacheValue"
          >
            <el-table-column
              :label="$t('number.serial')"
              type="index"
              width="120"
            ></el-table-column>
            <el-table-column
              :formatter="keyFormatter"
              :label="$t('name.key.cache')"
              :show-overflow-tooltip="true"
              align="center"
            >
            </el-table-column>
            <el-table-column
              :label="$t('operation')"
              align="center"
              class-name="small-padding fixed-width"
              width="120"
            >
              <template #default="scope">
                <el-button
                  icon="Delete"
                  link
                  type="danger"
                  @click="handleClearCacheKey(scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card :bordered="false" style="height: calc(100vh - 125px)">
          <template #header>
            <Document style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('content.cache') }}</span>
            <el-button
              icon="Refresh"
              link
              style="float: right; padding: 3px 0"
              type="primary"
              @click="handleClearCacheAll()"
              >{{ $t('all.clear') }}
            </el-button>
          </template>
          <el-form :model="cacheForm">
            <el-row :gutter="32">
              <el-col :offset="1" :span="22">
                <el-form-item :label="$t('name.cache')" prop="cacheName">
                  <el-input v-model="cacheForm.cacheName" :readOnly="true" />
                </el-form-item>
              </el-col>
              <el-col :offset="1" :span="22">
                <el-form-item :label="$t('key.name.cache')" prop="cacheKey">
                  <el-input v-model="cacheForm.cacheKey" :readOnly="true" />
                </el-form-item>
              </el-col>
              <el-col :offset="1" :span="22">
                <el-form-item :label="$t('content.cache')" prop="cacheValue">
                  <el-input
                    v-model="cacheForm.cacheValue"
                    :readOnly="true"
                    :rows="8"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script name="CacheList" setup>
  import { getCurrentInstance } from 'vue'
  import {
    clearCacheAll,
    clearCacheKey,
    clearCacheName,
    getCacheValue,
    listCacheKey,
    listCacheName
  } from '@/api/monitor/cache'

  const { proxy } = getCurrentInstance()

  const cacheNames = ref([])
  const cacheKeys = ref([])
  const cacheForm = ref({})
  const loading = ref(true)
  const subLoading = ref(false)
  const nowCacheName = ref('')
  const tableHeight = ref(window.innerHeight - 200)

  /** 查询缓存名称列表 */
  function getCacheNames() {
    loading.value = true
    listCacheName().then((response) => {
      cacheNames.value = response.data
      loading.value = false
    })
  }

  /** 刷新缓存名称列表 */
  function refreshCacheNames() {
    getCacheNames()
    proxy.$modal.msgSuccess(proxy.$t('cache.list.refresh.success'))
  }

  /** 清理指定名称缓存 */
  function handleClearCacheName(row) {
    clearCacheName(row.cacheName).then((response) => {
      proxy.$modal.msgSuccess(proxy.$t('name.cache.clear') + row.cacheName + proxy.$t('success'))
      getCacheKeys()
    })
  }

  /** 查询缓存键名列表 */
  function getCacheKeys(row) {
    const cacheName = row !== undefined ? row.cacheName : nowCacheName.value
    if (cacheName === '') {
      return
    }
    subLoading.value = true
    listCacheKey(cacheName).then((response) => {
      cacheKeys.value = response.data
      subLoading.value = false
      nowCacheName.value = cacheName
    })
  }

  /** 刷新缓存键名列表 */
  function refreshCacheKeys() {
    getCacheKeys()
    proxy.$modal.msgSuccess(proxy.$t('list.key.name.refresh.success'))
  }

  /** 清理指定键名缓存 */
  function handleClearCacheKey(cacheKey) {
    clearCacheKey(cacheKey).then((response) => {
      proxy.$modal.msgSuccess(proxy.$t('name.key.cache.clear') + cacheKey + proxy.$t('success'))
      getCacheKeys()
    })
  }

  /** 列表前缀去除 */
  function nameFormatter(row) {
    return row.cacheName.replace(':', '')
  }

  /** 键名前缀去除 */
  function keyFormatter(cacheKey) {
    return cacheKey.replace(nowCacheName.value, '')
  }

  /** 查询缓存内容详细 */
  function handleCacheValue(cacheKey) {
    getCacheValue(nowCacheName.value, cacheKey).then((response) => {
      cacheForm.value = response.data
    })
  }

  /** 清理全部缓存 */
  function handleClearCacheAll() {
    clearCacheAll().then((response) => {
      proxy.$modal.msgSuccess(proxy.$t('cache.all.clear.success'))
    })
  }

  getCacheNames()
</script>
