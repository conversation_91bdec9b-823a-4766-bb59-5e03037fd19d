<template>
  <div class="page-content">
    <high-query
      :formKey="data.formKey"
      :formName="data.formName"
      @load="search"
      @refresh="refresh"
      @search="search"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:queryCase:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:queryCase:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:queryCase:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:queryCase:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
    </el-row>

    <art-table
      v-loading="loading"
      :data="queryCaseList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="search"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column :label="$t('name.plan')" :show-overflow-tooltip="true" prop="title" />
      <el-table-column :label="$t('creator')" :show-overflow-tooltip="true" prop="createByText" />
      <el-table-column :label="$t('time.creation')" :show-overflow-tooltip="true" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('default.question')"
        :show-overflow-tooltip="true"
        prop="defaultCase"
      >
        <template #default="scope">
          <el-switch
            v-model="scope.row.defaultCase"
            :active-text="$t('yes')"
            :inactive-text="$t('no')"
            active-value="Y"
            inactive-value="N"
            inline-prompt
            @click="handleSwitch(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column :label="$t('form.belong')" :show-overflow-tooltip="true" prop="formName" />
    </art-table>

    <!-- 添加或修改查询方案对话框 -->

    <Drawer v-model="open">
      <template #title>
        <p>{{ title }}</p>
      </template>
      <template #btn_group>
        <el-row>
          <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm"
            >{{ $t('intl.btn.submit') }}
          </el-button>
        </el-row>
      </template>

      <template #content>
        <DrawerTitle>
          <template #default>
            {{ $t('intl.drawer.basicInfo') }}
          </template>
        </DrawerTitle>
        <DrawerGroup>
          <el-form ref="queryCaseRef" :model="form" :rules="rules">
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t('name.plan')" prop="title">
                  <el-input v-model="form.title" :placeholder="$t('name.plan.input')" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('default.question')" prop="defaultCase">
                  <el-select
                    v-model="form.defaultCase"
                    :placeholder="$t('plan.default.select')"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="dict in sys_yes_no"
                      :key="dict.value"
                      :label="$t(dict.type + '.' + dict.value)"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('form.belong')" prop="formKey">
                  <el-input v-model="form.formKey" :placeholder="$t('form.belong.input')" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </DrawerGroup>
      </template>
    </Drawer>
  </div>
</template>

<script name="QueryCase" setup>
  import { getCurrentInstance } from 'vue'
  import {
    addQueryCase,
    delQueryCase,
    getQueryCase,
    listQueryCase,
    updateQueryCase
  } from '@/api/system/queryCase'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import { listUser } from '@/api/system/user'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'

  const { proxy } = getCurrentInstance()

  const { sys_yes_no } = proxy.useDict('sys_yes_no')

  const queryCaseList = ref([])
  const open = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const names = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    formKey: 't_query_case',
    formName: proxy.$t('plan.query'),
    deptList: [],
    userList: [],
    form: {
      delQueryCase: 'Y'
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20
    },
    rules: {
      title: [{ required: true, message: proxy.$t('name.plan.empty'), trigger: 'blur' }],
      content: [{ required: true, message: proxy.$t('conditions.search.empty'), trigger: 'blur' }],
      createBy: [{ required: true, message: proxy.$t('creator.empty'), trigger: 'blur' }],
      createTime: [{ required: true, message: proxy.$t('time.creation.empty'), trigger: 'blur' }],
      updateBy: [{ required: true, message: proxy.$t('modifier.empty'), trigger: 'blur' }],
      updateTime: [
        { required: true, message: proxy.$t('time.modification.empty'), trigger: 'blur' }
      ],
      defaultCase: [{ required: true, message: proxy.$t('plan.default.empty'), trigger: 'change' }],
      formKey: [{ required: true, message: proxy.$t('form.belong.empty'), trigger: 'blur' }]
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  function refresh(query) {
    query.pageNum = 1
    search(query)
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  /**
   * 处理是否默认方案
   * @param data
   */
  const handleSwitch = (data) => {
    updateQueryCase(data)
      .then((res) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
      })
      .catch((err) => {
        data.defaultCase = data.defaultCase === 'Y' ? 'N' : 'Y'
      })
  }

  /**
   * 查询
   * @param query
   */
  function search(query) {
    if (query) {
      data.queryParams = { ...data.queryParams, ...query }
    }
    loading.value = true
    listQueryCase(data.queryParams)
      .then(({ data }) => {
        queryCaseList.value = data.records
        total.value = data.totalRow
        loading.value = false
      })
      .catch((err) => {
        loading.value = false
      })
  }

  // 取消按钮
  function cancel() {
    open.value = false
    reset()
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      title: null,
      content: null,
      createBy: null,
      createTime: null,
      updateBy: null,
      updateTime: null,
      defaultCase: 'Y',
      formKey: null
    }
    proxy.resetForm('queryCaseRef')
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id)
    names.value = selection.map((item) => item.title)

    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset()
    open.value = true
    title.value = proxy.$t('plan.query.add')
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset()
    const id = row.id || ids.value
    getQueryCase(id).then((response) => {
      form.value = response.data
      open.value = true
      title.value = proxy.$t('plan.query.modify')
    })
  }

  const loadingBtn = reactive({
    submit: false
  })
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['queryCaseRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true

        if (form.value.id != null) {
          updateQueryCase(form.value).then((response) => {
            proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
            open.value = false
            search()
          })
        } else {
          addQueryCase(form.value).then((response) => {
            proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
            open.value = false
            data.queryParams.pageNum = 1
            search()
          })
        }
      }
    })
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const idKeys = row.id || ids.value
    const nameArr = row.title || names.value
    proxy.$modal
      .confirm(`是否确认删除方案名为【${nameArr}】的数据项?`)
      .then(function () {
        return delQueryCase(idKeys)
      })
      .then(() => {
        search()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.downloadQueryCase(
      'system/queryCase/export',
      queryParams.value,
      `queryCase_${new Date().getTime()}.xlsx`
    )
  }
</script>
