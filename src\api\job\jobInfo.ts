import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listJob_info(data) {
  return request({
    url: '/xxl/jobinfo/list',
    method: 'get',
    params: data
  })
}

// 新增/修改
export function addJob_info(data) {
  return request({
    url: '/xxl/jobinfo/add',
    method: 'get',
    params: data
  })
}

/**
 * 修改任务
 * @param data
 * @returns {*}
 */
export function updateInfo(data) {
  return request({
    url: '/xxl/jobinfo/update',
    method: 'get',
    params: data
  })
}

// 获取执行器列表
export function executeHandler() {
  return request({
    url: '/xxl/jobinfo/executeHandler',
    method: 'get'
  })
}

//执行一次
export function executeOnes(params) {
  return request({
    url: '/xxl/jobinfo/trigger',
    method: 'get',
    params
  })
}

//获取注册节点
export function getRegNodeList(params) {
  return request({
    url: '/xxl/jobgroup/loadById',
    method: 'get',
    params
  })
}

//下一次执行时间
export function nextTime(params) {
  return request({
    url: '/xxl/jobinfo/nextTriggerTime',
    method: 'get',
    params
  })
}

//启动
export function start(params) {
  return request({
    url: '/xxl/jobinfo/start',
    method: 'get',
    params
  })
}

//停止
export function stop(params) {
  return request({
    url: '/xxl/jobinfo/stop',
    method: 'get',
    params
  })
}

//删除
export function remove(params) {
  return request({
    url: '/xxl/jobinfo/remove',
    method: 'get',
    params
  })
}

//编辑代码
export function editorCode(params) {
  return request({
    url: '/xxl/jobcode/code',
    method: 'get',
    params
  })
}

/**
 * 保存代码
 * @param params
 * @returns {*}
 */
export function saveCode(params) {
  return request({
    url: '/xxl/jobcode/save',
    method: 'get',
    params
  })
}

/**
 * 根据任务id获取任务信息
 * @param params
 * @returns {*}
 */
export function getById(params) {
  return request({
    url: '/xxl/jobinfo/getById/' + params,
    method: 'get'
  })
}
