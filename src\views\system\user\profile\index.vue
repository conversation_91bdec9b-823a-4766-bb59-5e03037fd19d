<template>
  <div class="page-content">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="box-card">
          <template v-slot:header>
            <div class="clearfix">
              <span>{{ $t('information.personal') }}</span>
            </div>
          </template>
          <div>
            <div class="text-center">
              <userAvatar />
            </div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item">
                <svg-icon icon-class="user" />
                $t('name.user')
                <div class="pull-right">{{ state.user.userName }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="phone" />
                $t('number.mobile')
                <div class="pull-right">{{ state.user.phonenumber }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="email" />
                $t('email.user')
                <div class="pull-right">{{ state.user.email }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="tree" />
                $t('department.affiliated')
                <div v-if="state.user.dept" class="pull-right"
                  >{{ state.user.dept.deptName }} / {{ state.postGroup }}
                </div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="peoples" />
                $t('role.affiliated')
                <div class="pull-right">{{ state.roleGroup }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="date" />
                $t('date.creation')
                <div class="pull-right">{{ state.user.createTime }}</div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card>
          <template v-slot:header>
            <div class="clearfix">
              <span>{{ $t('information.basic') }}</span>
            </div>
          </template>
          <el-tabs v-model="activeTab">
            <el-tab-pane :label="$t('information.basic')" name="userinfo">
              <userInfo :user="state.user" />
            </el-tab-pane>
            <el-tab-pane :label="$t('password.modify')" name="resetPwd">
              <resetPwd />
            </el-tab-pane>
            <el-tab-pane :label="$t('application.third.party')" name="otherApp">
              <otherApp :auths="state.auths" />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script name="Profile" setup>
  import { getCurrentInstance, reactive, ref } from 'vue'
  import userAvatar from './userAvatar'
  import userInfo from './userInfo'
  import resetPwd from './resetPwd'
  import otherApp from './otherApp'
  import { getUserProfile } from '@/api/system/user'
  import { useRoute } from 'vue-router'

  const { proxy } = getCurrentInstance()

  const route = useRoute()
  const activeTab = ref('userinfo')
  //绑定第三方应用登录
  const { params, query } = route
  if (query['activeTab']) {
    activeTab.value = query['activeTab']
  }
  const state = reactive({
    user: {},
    auths: [],
    roleGroup: {},
    postGroup: {}
  })

  function getUser() {
    getUserProfile().then((response) => {
      state.user = response.data
      state.roleGroup = response.roleGroup
      state.postGroup = response.postGroup
      state.auths = response.auths
    })
  }

  getUser()
</script>
