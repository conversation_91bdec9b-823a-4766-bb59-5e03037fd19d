import request from '@/utils/request'

// 查询高级查询列列表
export function listHigthQueryDetail(data) {
  return request({
    url: '/system/higthQueryDetail/list',
    method: 'post',
    data
  })
}

// 查询高级查询列详细
export function getHigthQueryDetail(id) {
  return request({
    url: '/system/higthQueryDetail/' + id,
    method: 'get'
  })
}

export function listByBusiKey(busiKey) {
  return request({
    url: '/system/higthQueryDetail/listByBusiKey/' + busiKey,
    method: 'get'
  })
}

// 新增高级查询列
export function addHigthQueryDetail(data) {
  return request({
    url: '/system/higthQueryDetail',
    method: 'post',
    data: data
  })
}

// 修改高级查询列
export function updateHigthQueryDetail(data) {
  return request({
    url: '/system/higthQueryDetail',
    method: 'put',
    data: data
  })
}

// 删除高级查询列
export function delHigthQueryDetail(id) {
  return request({
    url: '/system/higthQueryDetail/' + id,
    method: 'delete'
  })
}
