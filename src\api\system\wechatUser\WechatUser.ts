import request from '@/utils/request'

// 查询微信小程序列表
export function listWechatUser(data) {
  return request({
    url: '/system/wechatUser/list',
    method: 'post',
    data
  })
}

// 查询微信小程序详细
export function getWechatUser(id) {
  return request({
    url: '/system/wechatUser/' + id,
    method: 'get'
  })
}

// 新增微信小程序
export function addWechatUser(data) {
  return request({
    url: '/system/wechatUser',
    method: 'post',
    data: data
  })
}

// 修改微信小程序
export function updateWechatUser(data) {
  return request({
    url: '/system/wechatUser',
    method: 'put',
    data: data
  })
}

// 删除微信小程序
export function delWechatUser(id) {
  return request({
    url: '/system/wechatUser/' + id,
    method: 'delete'
  })
}
