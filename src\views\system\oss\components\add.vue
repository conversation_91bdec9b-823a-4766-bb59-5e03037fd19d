<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="ossRef" :model="form" :rules="rules">
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('sysOss.fileName')" prop="fileName">
                <el-input
                  v-model="form.fileName"
                  :placeholder="formatStr($t('input.please'), $t('sysOss.fileName'))"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('sysOss.originalName')" prop="originalName">
                <el-input
                  v-model="form.originalName"
                  :placeholder="formatStr($t('input.please'), $t('sysOss.originalName'))"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('sysOss.fileSuffix')" prop="fileSuffix">
                <el-input
                  v-model="form.fileSuffix"
                  :placeholder="formatStr($t('input.please'), $t('sysOss.fileSuffix'))"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="formatStr($t('sysOss.service'))" prop="service">
                <el-input
                  v-model="form.service"
                  :placeholder="formatStr($t('input.please'), $t('sysOss.service'))"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$t('sysOss.url')" prop="url">
                <el-input
                  v-model="form.url"
                  :placeholder="$t('higth.input.content')"
                  style="width: 100%"
                  type="textarea"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { formatStr } from '@/utils/ruoyi'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'
  import { computed, toRefs } from 'vue'
  import { addOss, getOss, updateOss } from '@/api/system/oss/Oss'

  const { proxy } = getCurrentInstance()

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {},
    rules: {
      fileName: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('sysOss.fileName'), proxy.$t('not.empty')),
          trigger: 'blur'
        }
      ],
      originalName: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('sysOss.originalName'), proxy.$t('not.empty')),
          trigger: 'blur'
        }
      ],
      fileSuffix: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('sysOss.fileSuffix'), proxy.$t('not.empty')),
          trigger: 'blur'
        }
      ],
      url: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('sysOss.url'), proxy.$t('not.empty')),
          trigger: 'blur'
        }
      ],
      service: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('sysOss.service'), proxy.$t('not.empty')),
          trigger: 'blur'
        }
      ]
    }
  })

  const { form, rules } = toRefs(data)
  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['ossRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true

        if (form.value.ossId != null) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateOss(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }
  const add = () => {
    addOss(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))

        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getOss(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      ossId: null,
      fileName: null,
      originalName: null,
      fileSuffix: null,
      url: null,
      service: null
    }
    proxy.resetForm('ossRef')
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }
</script>
