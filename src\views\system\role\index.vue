<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="search"
      @refresh="refresh"
      @search="search"
    />
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
    </el-row>

    <!-- 表格数据 -->
    <!--<el-table v-loading="loading" :data="roleList" @selection-change="handleSelectionChange" height="540px">-->
    <art-table
      v-loading="loading"
      :data="roleList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      row-key="roleId"
      @search="search"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column :label="$t('number.role')" prop="roleId" width="120" />
      <el-table-column
        :label="$t('name.role')"
        :show-overflow-tooltip="true"
        prop="roleName"
        width="150"
      >
        <template #default="{ row }">
          <el-link type="primary" @click="handleDetail(row)">{{ row.roleName }}</el-link>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('character.permission')"
        :show-overflow-tooltip="true"
        prop="roleKey"
        width="150"
      />
      <el-table-column :label="$t('order.display')" prop="roleSort" width="100" />
      <el-table-column :label="$t('status')" align="center" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column :label="$t('time.creation')" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-tooltip v-if="scope.row.roleId !== 1" :content="$t('action.modify')" placement="top">
            <el-button
              v-hasPermi="['system:role:edit']"
              icon="Edit"
              link
              type="primary"
              @click="handleUpdate(scope.row)"
            ></el-button>
          </el-tooltip>
          <el-tooltip v-if="scope.row.roleId !== 1" :content="$t('action.delete')" placement="top">
            <el-button
              v-hasPermi="['system:role:remove']"
              icon="Delete"
              link
              type="danger"
              @click="handleDelete(scope.row)"
            ></el-button>
          </el-tooltip>
          <el-tooltip
            v-if="scope.row.roleId !== 1"
            :content="$t('permission.data')"
            placement="top"
          >
            <el-button
              v-hasPermi="['system:role:edit']"
              icon="CircleCheck"
              link
              type="primary"
              @click="handleDataScope(scope.row)"
            ></el-button>
          </el-tooltip>
          <el-tooltip v-if="scope.row.roleId !== 1" :content="$t('user.assign')" placement="top">
            <el-button
              v-hasPermi="['system:role:edit']"
              icon="User"
              link
              type="primary"
              @click="handleAuthUser(scope.row)"
            ></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改角色对话框 -->
    <add v-model:show="open" :title="title" :id="roleId" @refreshList="refreshList" />

    <!-- 角色详情对话框 -->
    <detail v-model:show="detailShow" :title="title" :id="roleId" />

    <!-- 分配角色数据权限对话框 -->
    <dataScope
      v-model:show="openDataScope"
      :title="title"
      :id="roleId"
      @refreshList="refreshList"
    />
  </div>
</template>

<script name="Role" setup>
  import { getCurrentInstance } from 'vue'
  import { changeRoleStatus, delRole, listRole } from '@/api/system/role'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import add from './components/add.vue'
  import detail from './components/detail.vue'
  import dataScope from './components/dataScope.vue'

  const { proxy } = getCurrentInstance()

  const router = useRouter()

  const roleList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const roleId = ref('')
  const loading = ref(true)
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')
  const openDataScope = ref(false)

  const data = reactive({
    fromKey: 'role',
    fromName: 'role',
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      roleName: undefined,
      roleKey: undefined,
      status: undefined
    }
  })

  const { queryParams } = toRefs(data)

  function refresh(query) {
    query.pageNum = 1
    search(query)
  }

  function search(query) {
    if (query) {
      data.queryParams = { ...data.queryParams, ...query }
    }
    loading.value = true
    listRole(data.queryParams)
      .then(({ data }) => {
        roleList.value = data.records
        total.value = data.totalRow
        loading.value = false
      })
      .catch((err) => {
        loading.value = false
      })
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const roleIds = row.roleId || ids.value
    proxy.$modal
      .confirm(proxy.$t('confirm.delete.role.number') + roleIds + proxy.$t('item.data.question'))
      .then(function () {
        return delRole(roleIds)
      })
      .then(() => {
        search()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/role/export',
      {
        ...queryParams.value
      },
      `role_${new Date().getTime()}.xlsx`
    )
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.roleId)
    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  /** 角色状态修改 */
  function handleStatusChange(row) {
    let text = row.status === '0' ? proxy.$t('action.enable') : proxy.$t('action.disable')
    proxy.$modal
      .confirm(proxy.$t('confirm.action') + text + '""' + row.roleName + proxy.$t('role.question'))
      .then(function () {
        return changeRoleStatus(row.roleId, row.status)
      })
      .then(() => {
        proxy.$modal.msgSuccess(text + proxy.$t('status.success'))
      })
      .catch(function () {
        row.status = row.status === '0' ? '1' : '0'
      })
  }

  /** 分配用户 */
  function handleAuthUser(row) {
    router.push('/system/role-auth/user/' + row.roleId)
  }

  /** 所有部门节点数据 */
  function getDeptAllCheckedKeys() {
    // 目前被选中的部门节点
    let checkedKeys = deptRef.value.getCheckedKeys()
    // 半选中的部门节点
    let halfCheckedKeys = deptRef.value.getHalfCheckedKeys()
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)
    return checkedKeys
  }

  /** 重置新增的表单以及其他数据  */
  function reset() {
    if (menuRef.value != undefined) {
      menuRef.value.setCheckedKeys([])
    }
    menuExpand.value = false
    menuNodeAll.value = false
    deptExpand.value = true
    deptNodeAll.value = false
    form.value = {
      roleId: undefined,
      roleName: undefined,
      roleKey: undefined,
      roleSort: 0,
      status: '0',
      menuIds: [],
      deptIds: [],
      menuCheckStrictly: true,
      deptCheckStrictly: true,
      remark: undefined
    }
    proxy.resetForm('roleRef')
  }

  /** 添加角色 */
  function handleAdd() {
    roleId.value = ''
    title.value = proxy.$t('role.add')
    open.value = true
  }

  /** 修改角色 */
  function handleUpdate(row) {
    roleId.value = row.roleId || ids.value
    title.value = proxy.$t('role.modify')
    open.value = true
  }

  /** 查看详情操作 */
  function handleDetail(row) {
    roleId.value = row.roleId
    title.value = proxy.$t('role.detail')
    detailShow.value = true
  }

  /** 分配数据权限操作 */
  function handleDataScope(row) {
    roleId.value = row.roleId
    title.value = proxy.$t('permission.data.assign')
    openDataScope.value = true
  }

  const refreshList = (pageNum) => {
    if (pageNum) {
      data.queryParams.pageNum = 1
    }
    search()
  }

  search()
</script>
