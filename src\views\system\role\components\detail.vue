<template>
  <Drawer v-model="open" @open="init" :size="700">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form label-width="120px" style="width: 100%">
          <el-row :gutter="0">
            <!-- 角色编号 -->
            <el-col :span="12">
              <el-form-item :label="$t('number.role')">
                <span>{{ form.roleId }}</span>
              </el-form-item>
            </el-col>
            <!-- 角色名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.role')">
                <span>{{ form.roleName }}</span>
              </el-form-item>
            </el-col>
            <!-- 权限字符 -->
            <el-col :span="12">
              <el-form-item :label="$t('character.permission')">
                <span>{{ form.roleKey }}</span>
              </el-form-item>
            </el-col>
            <!-- 显示顺序 -->
            <el-col :span="12">
              <el-form-item :label="$t('order.display')">
                <span>{{ form.roleSort }}</span>
              </el-form-item>
            </el-col>
            <!-- 状态 -->
            <el-col :span="12">
              <el-form-item :label="$t('status')">
                <dict-tag :options="sys_normal_disable" :value="form.status" />
              </el-form-item>
            </el-col>
            <!-- 创建时间 -->
            <el-col :span="12">
              <el-form-item :label="$t('time.creation')">
                <span>{{ parseTime(form.createTime) }}</span>
              </el-form-item>
            </el-col>
            <!-- 数据权限 -->
            <el-col :span="12">
              <el-form-item :label="$t('scope.permission')">
                <span>{{ getDataScopeLabel(form.dataScope) }}</span>
              </el-form-item>
            </el-col>
            <!-- 备注 -->
            <el-col :span="24">
              <el-form-item :label="$t('remarks')">
                <span>{{ form.remark || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>

      <!-- 菜单权限 -->
      <DrawerTitle>
        <template #default>{{ $t('permission.menu') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-tree
          ref="menuRef"
          :data="menuOptions"
          :empty-text="$t('loading')"
          :props="{ label: 'label', children: 'children' }"
          class="tree-border"
          node-key="id"
          show-checkbox
          default-expand-all
          :check-strictly="true"
        ></el-tree>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs, nextTick } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { getRole } from '@/api/system/role'
  import { roleMenuTreeselect } from '@/api/system/menu'

  const { proxy } = getCurrentInstance()
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {},
    menuOptions: []
  })

  const { form, menuOptions } = toRefs(data)
  const menuRef = ref(null)

  /** 数据范围选项*/
  const dataScopeOptions = [
    { value: '1', label: proxy.$t('permissions.data.all') },
    { value: '2', label: proxy.$t('data.permission.custom') },
    { value: '3', label: proxy.$t('permission.data.department') },
    { value: '4', label: proxy.$t('permission.data.department.below') },
    { value: '5', label: proxy.$t('permission.data.only') }
  ]

  const getDataScopeLabel = (value) => {
    const option = dataScopeOptions.find((item) => item.value === value)
    return option ? option.label : '-'
  }

  const setI18nTitle = (el) => {
    el.forEach((el) => {
      el.label = proxy.$t(el.label)
      if (el.children?.length > 0) {
        setI18nTitle(el.children)
      }
    })
  }

  /** 根据角色ID查询菜单树结构 */
  function getRoleMenuTreeselect(roleId) {
    return roleMenuTreeselect(roleId).then((response) => {
      setI18nTitle(response.menus)
      data.menuOptions = response.menus
      return response
    })
  }

  const init = () => {
    reset()
    if (props.id) {
      const roleMenu = getRoleMenuTreeselect(props.id)
      getRole(props.id).then((response) => {
        data.form = response.data
        nextTick(() => {
          roleMenu.then((res) => {
            let checkedKeys = res.checkedKeys
            nextTick(() => {
              if (menuRef.value) {
                checkedKeys.forEach((v) => {
                  menuRef.value.setChecked(v, true, false)
                })
              }
            })
          })
        })
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {}
    data.menuOptions = []
  }
</script>

<style scoped>
  .tree-border {
    margin-top: 10px;
    border: 1px solid var(--el-border-color-light);
    background: var(--el-bg-color);
    border-radius: 4px;
    width: 100%;
    padding: 10px;
    min-height: 200px;
  }

  :deep(.el-tree-node__content) {
    height: 32px;
    line-height: 32px;
  }

  :deep(.el-tree-node__content:hover) {
    background-color: var(--el-color-primary-light-9);
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }

  :deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }
</style>
