<template>
  <view style="width: 100%">
    <el-row>
      <el-col :span="6">
        <el-select
          v-model="data.queryData.column"
          :placeholder="$t('select.please')"
          class=""
          filterable
          style="width: 100%"
          @change="(el) => handlerChange(el)"
        >
          <el-option
            v-for="item in options"
            :key="item.column"
            :label="$t(item.i18nCode)"
            :value="item.column"
          />
        </el-select>
      </el-col>
      <el-col :span="6">
        <!--             条件   -->
        <el-select
          v-model="data.queryData.condition"
          :placeholder="$t('select.please')"
          class="big-ml-3.5 condition-width"
          filterable
          @change="conditionChange"
        >
          <el-option
            v-for="item in conditions[data.queryData.type]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-col>
      <el-col :span="10">
        <div class="val-width big-ml-6">
          <!--             值   -->
          <el-input
            v-if="
              data.queryData?.commonType === undefined ||
              data.queryData?.commonType === 'input' ||
              data.queryData?.commonType === 'string'
            "
            v-model="data.queryData.value"
            :disabled="
              data.queryData.condition === 'isNull' || data.queryData.condition === 'isNotNull'
            "
            :placeholder="$t('higth.input.content')"
            clearable
            @keyup.enter="emits('search')"
          ></el-input>

          <el-select
            v-else-if="data.queryData?.commonType === 'dict'"
            v-model="data.queryData.value"
            :disabled="
              data.queryData.condition === 'isNull' || data.queryData.condition === 'isNotNull'
            "
            :placeholder="$t('select.please')"
            clearable
            style="width: 100%"
            @keyup.enter="emits('search')"
          >
            <el-option
              v-for="dict in data.dictList"
              :key="dict.value"
              :label="$t(dict.type + '.' + dict.value)"
              :value="dict.value"
            />
          </el-select>

          <el-date-picker
            v-else-if="data.queryData?.commonType === 'date'"
            v-model="data.queryData.value"
            :disabled="
              data.queryData.condition === 'isNull' || data.queryData.condition === 'isNotNull'
            "
            :placeholder="$t('select.please')"
            clearable
            style="width: 100%"
            type="date"
            value-format="YYYY-MM-DD"
            @keyup.enter="emits('search')"
          >
          </el-date-picker>

          <el-select
            v-else-if="data.queryData?.commonType === 'user'"
            v-model="data.queryData.value"
            :disabled="
              data.queryData.condition === 'isNull' || data.queryData.condition === 'isNotNull'
            "
            :placeholder="$t('select.please')"
            :remote-method="handleUserSelect"
            clearable
            filterable
            remote
            remote-show-suffix
            reserve-keyword
            style="width: 100%"
            @keyup.enter="emits('search')"
          >
            <el-option
              v-for="item in data.userList"
              :key="item.nickName"
              :label="item.nickName"
              :value="item.userName"
            />
          </el-select>

          <el-select
            v-else-if="data.queryData?.commonType === 'dept'"
            v-model="data.queryData.value"
            :disabled="
              data.queryData.condition === 'isNull' || data.queryData.condition === 'isNotNull'
            "
            :placeholder="$t('select.please')"
            :remote-method="handleDeptSelect"
            clearable
            filterable
            remote
            remote-show-suffix
            reserve-keyword
            style="width: 100%"
            @keyup.enter="emits('search')"
          >
            <el-option
              v-for="item in data.deptList"
              :key="item.deptName"
              :label="item.deptName"
              :value="item.deptId"
            />
          </el-select>

          <!--通用联想查询-->
          <el-select
            v-else-if="data.queryData?.commonType === 'common'"
            v-model="data.queryData.value"
            :disabled="
              data.queryData.condition === 'isNull' || data.queryData.condition === 'isNotNull'
            "
            :placeholder="$t('select.please')"
            :remote-method="handleCommonSelect"
            clearable
            filterable
            remote
            reserve-keyword
            style="width: 100%"
            @keyup.enter="emits('search')"
          >
            <el-option
              v-for="item in data.commonList"
              :key="item.key"
              :label="item.val"
              :value="item.key"
            />
          </el-select>
        </div>
      </el-col>
    </el-row>
  </view>
</template>

<script lang="ts" setup>
  import { getCurrentInstance, reactive, watchEffect } from 'vue'
  import { searchKeyword } from '@/api/system/user'
  import { searchKeywordDept } from '@/api/system/dept'
  import { commonQuery } from '@/api/system/queryCase'

  const { proxy } = getCurrentInstance()

  const emits = defineEmits(['update:value', 'search'])
  const props = defineProps({
    options: {
      type: Array,
      default: () => []
    },
    default: {}
  })
  const data = reactive({
    dictList: [],
    deptList: [],
    userList: [],
    commonList: [],
    queryData: {
      column: props.default?.column,
      condition: props.default?.condition,
      type: props.default?.type,
      isDisable: props.default?.isDisable,
      value: props.default?.value,
      ruleAlias: props.default?.ruleAlias,
      dictKey: props.default?.dictKey,
      commonType: props.default?.commonType
    }
  })
  watchEffect(() => {
    if (props.default) {
      data.queryData = { ...props.default }
    }
  })

  const handleUserSelect = (item: Record<string, any>) => {
    if (item === '') {
      data.userList = []
      return
    }
    searchKeyword(item).then((res) => {
      data.userList = res.data
    })
  }
  const handleDeptSelect = (item: Record<string, any>) => {
    if (item === '') {
      data.deptList = []
      return
    }
    searchKeywordDept(item).then((res) => {
      data.deptList = res.data
    })
  }
  const searchKeywordCommon = (item: Record<string, any>) => {
    return commonQuery({
      tableName: props.default?.tableName,
      target: props.default?.targetField,
      where: props.default?.whereField,
      val: item
    })
  }

  const handleCommonSelect = (item: Record<string, any>) => {
    if (item === '') {
      data.commonList = []
      return
    }
    searchKeywordCommon(item).then((res) => {
      data.commonList = res.data
    })
  }

  function conditionChange(val) {
    if (val.endsWith('Null')) {
      data.queryData.value = ''
    }
  }

  const conditions = reactive({
    num: [
      {
        label: proxy.$t('higth.query.equal'), // 等于
        value: '=',
        default: true
      },
      {
        label: proxy.$t('higth.query.greaterEqual'), // 大于等于
        value: '>=',
        default: true
      },
      {
        label: proxy.$t('higth.query.greater'), // 大于
        value: '>'
      },
      {
        label: proxy.$t('higth.query.less'), // 小于
        value: '<'
      },
      {
        label: proxy.$t('higth.query.lessEqual'), // 小于等于
        value: '<='
      },
      {
        label: proxy.$t('higth.query.notEqual'), // 不等于
        value: '!='
      },
      {
        label: proxy.$t('higth.query.isNull'), // 为空
        value: 'isNull',
        disable: true
      },
      {
        label: proxy.$t('higth.query.isNotNull'), // 不为空
        value: 'isNotNull',
        disable: true
      }
    ],
    string: [
      {
        label: proxy.$t('higth.query.equal'), // 等于
        value: '=',
        default: true
      },
      {
        label: proxy.$t('higth.query.like'), // 包含
        value: 'like'
      },
      {
        label: proxy.$t('higth.query.greaterEqual'), // 大于等于
        value: '>='
      },
      {
        label: proxy.$t('higth.query.greater'), // 大于
        value: '>'
      },
      {
        label: proxy.$t('higth.query.less'), // 小于
        value: '<'
      },
      {
        label: proxy.$t('higth.query.lessEqual'), // 小于等于
        value: '<='
      },
      {
        label: proxy.$t('higth.query.notEqual'), // 不等于
        value: '!='
      },
      {
        label: proxy.$t('higth.query.isNull'), // 为空
        value: 'isNull',
        disable: true
      },
      {
        label: proxy.$t('higth.query.isNotNull'), // 不为空
        value: 'isNotNull',
        disable: true
      },
      {
        label: proxy.$t('higth.query.notLike'), // 不包含
        value: 'notLike'
      },
      {
        label: proxy.$t('higth.query.leftLike'), // 左包含
        value: 'leftLike'
      },
      {
        label: proxy.$t('higth.query.rightLike'), // 右包含
        value: 'rightLike'
      }
    ],
    date: [
      {
        label: proxy.$t('higth.query.equal'), // 等于
        value: '=',
        default: true
      },
      {
        label: proxy.$t('higth.query.greater'), // 大于
        value: '>'
      },
      {
        label: proxy.$t('higth.query.greaterEqual'), // 大于等于
        value: '>='
      },

      {
        label: proxy.$t('higth.query.less'), // 小于
        value: '<'
      },
      {
        label: proxy.$t('higth.query.lessEqual'), // 小于等于
        value: '<='
      },
      {
        label: proxy.$t('higth.query.notEqual'), // 不等于
        value: '!='
      },
      {
        label: proxy.$t('higth.query.isNull'), // 为空
        value: 'isNull',
        disable: true
      },
      {
        label: proxy.$t('higth.query.isNotNull'), // 不为空
        value: 'isNotNull',
        disable: true
      }
    ],
    select: [
      {
        label: proxy.$t('higth.query.equal'), // 等于
        value: '='
      },
      {
        label: proxy.$t('higth.query.notEqual'), // 不等于
        value: '!=',
        default: true
      }
    ]
  })

  function handlerChange(val: any) {
    let find = props.options.find((el) => el.column === val)
    data.queryData = { ...find }
    if (data.queryData?.commonType === 'dict') {
      data.dictList = proxy.useDict(data.queryData.dictKey)[data.queryData.dictKey]
    }
  }

  watchEffect((el) => {
    emits('update:value', data.queryData)
  })

  watchEffect((el) => {
    init()
  })

  function clear() {
    data.queryData = { ...props.default }
  }

  defineExpose({ clear })

  function init() {
    if (data.queryData.commonType === 'dict') {
      data.dictList = proxy.useDict(data.queryData.dictKey)[data.queryData.dictKey]
    } else if (data.queryData.commonType === 'user') {
      handleUserSelect(data.queryData.value)
    } else if (data.queryData.commonType === 'dept') {
      handleDeptSelect(data.queryData.value)
    }
  }

  init()
</script>

<style scoped>
  .key-width {
    width: 110%;
  }

  .val-width {
    width: 100%;
  }

  :deep(input) {
    font-size: 14px !important;
    font-weight: 700;
  }
</style>
