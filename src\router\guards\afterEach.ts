import type { Router, RouteLocationNormalized } from 'vue-router'
import NProgress from 'nprogress'

/**
 * 路由全局后置守卫
 */
export function setupAfterEachGuard(router: Router): void {
  router.afterEach((to: RouteLocationNormalized, from: RouteLocationNormalized) => {
    // 关闭进度条
    NProgress.done()

    // 可以在这里添加页面访问统计等逻辑
    if (import.meta.env.DEV) {
      console.log(`路由导航: ${from.path} -> ${to.path}`)
    }
  })
}

/**
 * 路由错误处理
 */
export function setupRouterErrorHandler(router: Router): void {
  router.onError((error) => {
    console.error('路由错误:', error)
    NProgress.done()

    // 可以在这里添加错误上报逻辑
    if (import.meta.env.PROD) {
      // 生产环境下可以上报错误
      // errorReporter.report(error)
    }
  })
}
