<template>
  <div class="spec-table">
    <!-- 批量设置区域 -->
    <div class="batch-setting">
      <el-form :inline="true">
        <el-form-item label="批量设置">
          <el-input v-model="batchForm.price" placeholder="价格" type="number">
            <template #append>元</template>
          </el-input>
          <el-input v-model="batchForm.costPrice" placeholder="成本价" type="number">
            <template #append>元</template>
          </el-input>
          <el-input v-model="batchForm.stock" placeholder="库存" type="number">
            <template #append>件</template>
          </el-input>
          <el-input v-model="batchForm.firstRebate" placeholder="一级返佣" type="number">
            <template #append>%</template>
          </el-input>
          <el-input v-model="batchForm.secondRebate" placeholder="二级返佣" type="number">
            <template #append>%</template>
          </el-input>
          <el-button type="primary" @click="applyBatchSetting">应用</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 规格表格 -->
    <el-form ref="ruleForm" :model="formData" :rules="rules">
      <el-table :data="formData.specCombinations" :span-method="arraySpanMethod" border>
        <!-- 规格列 -->
        <el-table-column
          v-for="spec in props.specs"
          :key="spec.specName"
          :label="spec.specName"
          :prop="spec.specName"
        />
        <!-- 图片上传列 -->
        <el-table-column label="规格图片" width="120">
          <template #default="{ row }">
            <image-upload
              v-model="row.image"
              :isShowTip="false"
              :limit="1"
              height="50px"
              width="50px"
            />
          </template>
        </el-table-column>

        <!-- 价格列 -->
        <el-table-column label="价格" width="180">
          <template #default="{ row, $index }">
            <div class="table-form-item">
              <el-form-item
                :prop="`specCombinations.${$index}.price`"
                :rules="rules.price"
                class="form-item"
              >
                <el-input
                  v-model.number="row.price"
                  :min="0"
                  class="table-input"
                  type="number"
                  @blur="validateField(`specCombinations.${$index}.price`)"
                >
                  <template #append>元</template>
                </el-input>
              </el-form-item>
            </div>
          </template>
        </el-table-column>

        <!-- 成本价列 -->
        <el-table-column label="成本价" width="180">
          <template #default="{ row, $index }">
            <div class="table-form-item">
              <el-form-item
                :prop="`specCombinations.${$index}.costPrice`"
                :rules="rules.costPrice"
                class="form-item"
              >
                <el-input
                  v-model.number="row.costPrice"
                  :min="0"
                  class="table-input"
                  type="number"
                  @blur="validateField(`specCombinations.${$index}.costPrice`)"
                >
                  <template #append>元</template>
                </el-input>
              </el-form-item>
            </div>
          </template>
        </el-table-column>

        <!-- 库存列 -->
        <el-table-column label="库存" width="180">
          <template #default="{ row, $index }">
            <div class="table-form-item">
              <el-form-item
                :prop="`specCombinations.${$index}.stock`"
                :rules="rules.stock"
                class="form-item"
              >
                <el-input
                  v-model.number="row.stock"
                  :min="0"
                  class="table-input"
                  type="number"
                  @blur="validateField(`specCombinations.${$index}.stock`)"
                >
                  <template #append>件</template>
                </el-input>
              </el-form-item>
            </div>
          </template>
        </el-table-column>
        <!-- 一级返佣 -->
        <el-table-column label="一级返佣（%）" width="180">
          <template #default="{ row }">
            <div class="table-form-item">
              <el-input v-model="row.firstRebate" class="table-input" placeholder="选填" />
            </div>
          </template>
        </el-table-column>
        <!-- 二级返佣 -->
        <el-table-column label="二级返佣（%）" width="180">
          <template #default="{ row }">
            <div class="table-form-item">
              <el-input v-model="row.secondRebate" class="table-input" placeholder="选填" />
            </div>
          </template>
        </el-table-column>
        <!-- 会员价 -->
        <!--<el-table-column label="会员价" width="180">-->
        <!--  <template #default="{ row }">-->
        <!--    <div class="table-form-item">-->
        <!--      <el-input v-model="row.vipPrice" class="table-input" placeholder="选填" />-->
        <!--    </div>-->
        <!--  </template>-->
        <!--</el-table-column>-->
        <!-- 货号列 -->
        <!--<el-table-column label="货号" width="180">-->
        <!--  <template #default="{ row }">-->
        <!--    <div class="table-form-item">-->
        <!--      <el-input v-model="row.secondRebate" placeholder="选填" class="table-input" />-->
        <!--    </div>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!-- 默认规格列 -->
        <el-table-column fixed="right" label="默认规格" width="100">
          <template #default="{ row }">
            <el-radio
              v-model="defaultSpec"
              :label="getSpecKey(row)"
              @change="handleDefaultSpecChange(row)"
            >
              &nbsp;
            </el-radio>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script setup>
  import ImageUpload from '@/components/ImageUpload/index.vue' // 导入 ImageUpload 组件
  import md5 from 'js-md5'
  import { ref, watch } from 'vue'

  const ruleForm = ref(null)
  const props = defineProps({
    product: {
      type: Object,
      default: () => ({})
    },
    specs: {
      type: Array,
      default: () => []
    },
    modelValue: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits(['update:modelValue'])

  // 批量设置表单
  const batchForm = ref({
    price: '',
    costPrice: '',
    stock: '',
    firstRebate: '',
    secondRebate: ''
  })

  // 默认规格
  const defaultSpec = ref('')

  // 初始化默认规格
  const initDefaultSpec = () => {
    const defaultItem = formData.value.specCombinations.find((item) => item.isDefault === 'Y')
    if (defaultItem) {
      defaultSpec.value = getSpecKey(defaultItem)
    }
  }

  // 获取规格的唯一键
  const getSpecKey = (row) => {
    return Object.entries(row)
      .filter(([key]) => props.specs.some((spec) => spec.specName === key))
      .map(([_, value]) => value)
      .join('_')
  }

  // 处理默认规格变更
  const handleDefaultSpecChange = (row) => {
    const newCombinations = formData.value.specCombinations.map((item) => ({
      ...item,
      isDefault: getSpecKey(item) === getSpecKey(row) ? 'Y' : 'N'
    }))
    formData.value.specCombinations = newCombinations
    emit('update:modelValue', newCombinations)
  }

  // 修改为响应式数据
  const formData = ref({
    specCombinations: []
  })

  // 修改获取规格值的方法
  const getSpecValues = (row) => {
    const specValueObj = {}
    props.specs.forEach((spec) => {
      if (row[spec.specName]) {
        specValueObj[spec.specName] = row[spec.specName]
      }
    })
    return JSON.stringify(specValueObj) // 返回包含规格名和规格值的JSON字符串
  }

  const getSpecValuesMd5 = (row) => {
    return props.specs
      .map((spec) => row[spec.specName])
      .filter(Boolean)
      .sort()
      .join('_')
  }

  // 计算规格组合的 MD5 值
  const getSpecMd5 = (row) => {
    const specValues = getSpecValuesMd5(row)
    return md5(specValues)
  }

  // 生成规格组合的方法
  const generateSpecCombinations = () => {
    const validSpecs = props.specs
    if (!validSpecs?.length) return []

    const combinations = validSpecs.reduce((acc, spec) => {
      if (acc.length === 0) {
        return spec.specValues.map((value) => {
          const specObj = { [spec.specName]: value }
          return {
            ...specObj,
            specValues: JSON.stringify(specObj) // 使用JSON字符串格式
          }
        })
      }

      const newAcc = []
      acc.forEach((item) => {
        spec.specValues.forEach((value) => {
          const newObj = { ...item }
          newObj[spec.specName] = value
          // 构建包含所有规格名和值的对象
          const specValuesObj = {}
          validSpecs.forEach((s) => {
            if (newObj[s.specName]) {
              specValuesObj[s.specName] = newObj[s.specName]
            }
          })
          newObj.specValues = JSON.stringify(specValuesObj)
          newAcc.push(newObj)
        })
      })
      return newAcc
    }, [])

    // 合并现有数据
    return combinations.map((combo) => {
      const existing = props.modelValue.find((item) => {
        const itemSpecValues = getSpecValues(item)
        const comboSpecValues = getSpecValues(combo)
        return itemSpecValues === comboSpecValues
      })

      const result = {
        ...combo,
        secondRebate: existing?.secondRebate ?? '',
        firstRebate: existing?.firstRebate ?? '',
        vipPrice: existing?.vipPrice ?? '',
        price: existing?.price ?? '',
        costPrice: existing?.costPrice ?? '',
        stock: existing?.stock ?? '',
        sku: existing?.sku ?? '',
        image: existing?.image ?? '',
        specMd5: getSpecMd5(combo),
        isDefault: existing?.isDefault ?? 'N',
        specValues: existing?.specValues ?? getSpecValues(combo)
      }

      // 如果是默认规格，设置 defaultSpec
      if (result.isDefault === 'Y') {
        defaultSpec.value = getSpecKey(result)
      }

      return result
    })
  }

  // 监听规格变化，更新组合
  watch(
    () => props.specs,
    () => {
      // 使用 nextTick 确保 DOM 更新
      nextTick(() => {
        formData.value.specCombinations = generateSpecCombinations()
      })
    },
    { immediate: true, deep: true } // 添加 deep: true 以确保深层监听
  )

  // 监听 modelValue 变化
  watch(
    () => props.modelValue,
    (newVal) => {
      if (JSON.stringify(newVal) !== JSON.stringify(formData.value.specCombinations)) {
        formData.value.specCombinations = generateSpecCombinations()
      }
    }
  )

  // 监听表单数据变化
  watch(
    () => formData.value.specCombinations,
    (newVal) => {
      if (JSON.stringify(newVal) !== JSON.stringify(props.modelValue)) {
        emit('update:modelValue', newVal)
      }
    },
    { deep: true }
  )

  // 计算每个规格的跨行数
  const getSpanCount = (index) => {
    const { specs } = props
    let count = 1
    for (let i = index + 1; i < specs.length; i++) {
      count *= specs[i].specValues.length
    }
    return count
  }

  // 单元格合并方法
  const arraySpanMethod = ({ row, column, rowIndex, columnIndex }) => {
    if (columnIndex < props.specs.length) {
      const specIndex = columnIndex
      const count = getSpanCount(specIndex)

      if (rowIndex % count === 0) {
        return {
          rowspan: count,
          colspan: 1
        }
      } else {
        return {
          rowspan: 0,
          colspan: 0
        }
      }
    }
  }

  // 应用批量设置
  const applyBatchSetting = () => {
    const newCombinations = formData.value.specCombinations.map((item) => ({
      ...item,
      price: batchForm.value.price || item.price,
      costPrice: batchForm.value.costPrice || item.costPrice,
      stock: batchForm.value.stock || item.stock,
      firstRebate: batchForm.value.firstRebate || item.firstRebate,
      secondRebate: batchForm.value.secondRebate || item.secondRebate
    }))

    emit('update:modelValue', newCombinations)

    // 清空批量设置表单

    batchForm.value = {
      price: '',
      costPrice: '',
      stock: '',
      firstRebate: '',
      secondRebate: ''
    }
  }
  const validate = async () => {
    try {
      await ruleForm.value.validate()
      return true
    } catch (e) {
      return false
    }
  }
  defineExpose({
    validate
  })

  const formRef = ref(null)

  // 验证规则
  const rules = {
    price: [
      { required: true, message: '请输入价格' },
      {
        type: 'number',
        min: 0.01,
        message: '价格必须大于0',
        trigger: ['blur', 'change'],
        transform: (value) => Number(value)
      }
    ],
    costPrice: [
      {
        type: 'number',
        min: 0.01,
        message: '成本价必须大于0',
        trigger: ['blur', 'change'],
        transform: (value) => Number(value)
      }
    ],
    stock: [
      { required: true, message: '请输入库存' },
      {
        type: 'number',
        min: 1,
        message: '库存必须大于0',
        trigger: ['blur', 'change'],
        transform: (value) => Number(value)
      }
    ]
  }

  // 验证方法
  const validateField = (prop) => {
    formRef.value?.validateField(prop)
  }

  // 当需要向父组件提交数据时
  const handleSubmit = () => {
    const formattedData = formData.value.specCombinations.map((item) => ({
      ...item,
      specValues: getSpecValues(item), // 确保获取最新的规格值
      specMd5: getSpecMd5(item)
    }))
    emit('update:modelValue', formattedData)
  }

  // 在生成规格组合后初始化默认规格
  watch(
    () => formData.value.specCombinations,
    () => {
      initDefaultSpec()
    },
    { immediate: true }
  )
</script>

<style lang="scss" scoped>
  .spec-table {
    .batch-setting {
      margin-bottom: 20px;

      .el-input {
        width: 160px;
        margin-right: 10px;
      }
    }

    :deep(.el-table) {
      .el-table__cell {
        text-align: center;
      }
    }

    :deep(.el-table) {
      .el-table__cell {
        padding: 8px;
      }
    }

    :deep(.el-table__row) {
      td {
        height: 70px; // 固定单元格高度
      }
    }

    .table-form-item {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      :deep(.el-form-item) {
        margin-bottom: 0;
        width: 100%;

        // 当表单项有错误时添加底部间距
        &.is-error {
          margin-bottom: 18px;
        }
      }

      :deep(.el-form-item__content) {
        width: 100%;
        margin: 0 !important;
      }

      :deep(.el-form-item__error) {
        position: absolute;
        left: 0;
        bottom: -20px;
        font-size: 12px;
        line-height: 1;
        color: #f56c6c;
        white-space: nowrap;
      }
    }

    .table-input {
      width: 100%;

      :deep(.el-input__wrapper) {
        width: 100%;
      }
    }
  }
</style>
