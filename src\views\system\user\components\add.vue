<template>
  <Drawer v-model="open" @open="init" :size="700">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm">
          {{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="userRef" :model="form" :rules="rules" label-width="90px">
          <el-row>
            <!-- 用户昵称 -->
            <el-col :span="12">
              <el-form-item :label="$t('nickname.user')" prop="nickName">
                <el-input
                  v-model="form.nickName"
                  :placeholder="$t('nickname.user.input')"
                  maxlength="30"
                />
              </el-form-item>
            </el-col>
            <!-- 归属部门 -->
            <el-col :span="12">
              <el-form-item :label="$t('department.affiliation')" prop="deptId">
                <el-tree-select
                  v-model="form.deptId"
                  :data="deptOptions"
                  :placeholder="$t('department.affiliation.select')"
                  :props="{ value: 'id', label: 'label', children: 'children' }"
                  check-strictly
                  value-key="id"
                  filterable
                  clearable
                />
              </el-form-item>
            </el-col>

            <!-- 手机号码 -->
            <el-col :span="12">
              <el-form-item :label="$t('number.mobile')" prop="phonenumber">
                <el-input
                  v-model="form.phonenumber"
                  :placeholder="$t('number.mobile.input')"
                  maxlength="11"
                />
              </el-form-item>
            </el-col>
            <!-- 邮箱 -->
            <el-col :span="12">
              <el-form-item :label="$t('email')" prop="email">
                <el-input v-model="form.email" :placeholder="$t('email.input')" maxlength="50" />
              </el-form-item>
            </el-col>

            <!-- 用户名称 -->
            <el-col :span="12">
              <el-form-item
                v-if="form.userId === undefined"
                :label="$t('name.user')"
                prop="userName"
              >
                <el-input
                  v-model="form.userName"
                  :placeholder="$t('name.user.input')"
                  maxlength="30"
                />
              </el-form-item>
            </el-col>
            <!-- 用户密码 -->
            <el-col :span="12">
              <el-form-item
                v-if="form.userId === undefined"
                :label="$t('password.user')"
                prop="password"
              >
                <el-input
                  v-model="form.password"
                  :placeholder="$t('password.user.input')"
                  maxlength="20"
                  show-password
                  type="password"
                />
              </el-form-item>
            </el-col>

            <!-- 用户性别 -->
            <el-col :span="12">
              <el-form-item :label="$t('gender.user')">
                <el-select v-model="form.sex" :placeholder="$t('select.please')">
                  <el-option
                    v-for="dict in sys_user_sex"
                    :key="dict.value"
                    :label="$t(dict.type + '.' + dict.value)"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 状态 -->
            <el-col :span="12">
              <el-form-item :label="$t('status')">
                <el-radio-group v-model="form.status">
                  <el-radio
                    v-for="dict in sys_normal_disable"
                    :key="dict.value"
                    :value="dict.value"
                  >
                    {{ $t(dict.type + '.' + dict.value) }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <!-- 岗位 -->
            <el-col :span="12">
              <el-form-item :label="$t('post')">
                <el-select
                  v-model="form.postIds"
                  :placeholder="$t('select.please')"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option
                    v-for="item in postOptions"
                    :key="item.postId"
                    :disabled="item.status === 1"
                    :label="item.postName"
                    :value="item.postId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 角色 -->
            <el-col :span="12">
              <el-form-item :label="$t('role')">
                <el-select
                  v-model="form.roleIds"
                  :placeholder="$t('select.please')"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option
                    v-for="item in roleOptions"
                    :key="item.roleId"
                    :disabled="item.status === 1"
                    :label="item.roleName"
                    :value="item.roleId"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 备注 -->
            <el-col :span="24">
              <el-form-item :label="$t('remarks')">
                <el-input
                  v-model="form.remark"
                  :placeholder="$t('content.input')"
                  type="textarea"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { addUser, getUser, updateUser, deptTreeSelect } from '@/api/system/user'

  const { proxy } = getCurrentInstance()
  const { sys_user_sex, sys_normal_disable } = proxy.useDict('sys_user_sex', 'sys_normal_disable')

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {},
    rules: {
      userName: [
        { required: true, message: proxy.$t('name.user.empty'), trigger: 'blur' },
        {
          min: 2,
          max: 20,
          message: proxy.$t('length.username.between'),
          trigger: 'blur'
        }
      ],
      nickName: [{ required: true, message: proxy.$t('nickname.user.empty'), trigger: 'blur' }],
      password: [
        { required: true, message: proxy.$t('password.user.empty'), trigger: 'blur' },
        {
          min: 5,
          max: 20,
          message: proxy.$t('length.password.user'),
          trigger: 'blur'
        },
        { pattern: /^[^<>"'|\\]+$/, message: proxy.$t('characters.illegal'), trigger: 'blur' }
      ],
      email: [
        { type: 'email', message: proxy.$t('address.email.correct'), trigger: ['blur', 'change'] }
      ],
      phonenumber: [
        {
          pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
          message: proxy.$t('number.mobile.correct'),
          trigger: 'blur'
        }
      ]
    },
    deptOptions: [],
    postOptions: [],
    roleOptions: []
  })

  const { form, rules, deptOptions, postOptions, roleOptions } = toRefs(data)

  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['userRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true
        if (form.value.userId != undefined) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateUser(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const add = () => {
    addUser(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    getDeptTree()
    if (props.id) {
      getUser(props.id).then((response) => {
        data.form = response.data
        data.postOptions = response.posts
        data.roleOptions = response.roles
        data.form.postIds = response.postIds
        data.form.roleIds = response.roleIds
        data.form.password = ''
      })
    } else {
      getUser().then((response) => {
        data.postOptions = response.posts
        data.roleOptions = response.roles
        data.form.password = response.initPassword || '123456'
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      userId: undefined,
      deptId: undefined,
      userName: undefined,
      nickName: undefined,
      password: undefined,
      phonenumber: undefined,
      email: undefined,
      sex: undefined,
      status: '0',
      remark: undefined,
      postIds: [],
      roleIds: []
    }
    proxy.resetForm('userRef')
  }

  /** 查询部门下拉树结构 */
  function getDeptTree() {
    deptTreeSelect().then((response) => {
      data.deptOptions = response.data
    })
  }
</script>
