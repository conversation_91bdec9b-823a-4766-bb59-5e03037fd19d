import request from '@/utils/request'

// 查询产品分类列表
export function listProductCategories(data) {
  return request({
    url: '/system/productCategories/list',
    method: 'post',
    data
  })
}

// 查询产品分类详细
export function getProductCategories(id) {
  return request({
    url: '/system/productCategories/' + id,
    method: 'get'
  })
}

// 新增产品分类
export function addProductCategories(data) {
  return request({
    url: '/system/productCategories',
    method: 'post',
    data: data
  })
}

// 修改产品分类
export function updateProductCategories(data) {
  return request({
    url: '/system/productCategories',
    method: 'put',
    data: data
  })
}

// 删除产品分类
export function delProductCategories(id) {
  return request({
    url: '/system/productCategories/' + id,
    method: 'delete'
  })
}
