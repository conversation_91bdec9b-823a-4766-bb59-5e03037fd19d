<template>
  <Drawer v-model="open" size="80%" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #btn_group>
      <el-row>
        <el-button :loading="isLoading" type="primary" @click="submitForm"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerGroup>
        <el-form ref="productsRef" :model="form" :rules="rules">
          <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="基本信息" name="base_info">
              <el-row>
                <!--产品名称-->
                <el-col :span="12">
                  <el-form-item :label="$t('products.name')" prop="name">
                    <el-input
                      v-model="form.name"
                      :placeholder="formatStr($t('input.please'), $t('products.name'))"
                    />
                  </el-form-item>
                </el-col>

                <!--副标题/卖点-->
                <el-col :span="12">
                  <el-form-item :label="$t('products.subtitle')" prop="subtitle">
                    <el-input
                      v-model="form.subtitle"
                      :placeholder="formatStr($t('input.please'), $t('products.subtitle'))"
                    />
                  </el-form-item>
                </el-col>

                <!--首页封面图-->
                <el-col :span="24">
                  <el-form-item :label="$t('products.coverImage')" prop="coverImage">
                    <image-upload v-model="form.coverImage" :is-show-tip="false" :limit="1" />
                  </el-form-item>
                </el-col>

                <!--详情页图片，支持多张-->
                <el-col :span="24">
                  <el-form-item :label="$t('products.detailImages')">
                    <image-upload v-model="form.detailImages" :is-show-tip="false" :limit="4" />
                  </el-form-item>
                </el-col>

                <!--预售价-->
                <el-col v-if="form.status === 'pre_sales'" :span="12">
                  <el-form-item :label="$t('products.presalePrice')" prop="presalePrice">
                    <el-input
                      v-model="form.presalePrice"
                      :placeholder="formatStr($t('input.please'), $t('products.presalePrice'))"
                    />
                  </el-form-item>
                </el-col>

                <!--状态:delist=下架,listing=上架,pre_sales=预售中-->
                <el-col :span="12">
                  <el-form-item :label="$t('products.status')">
                    <el-radio-group v-model="form.status">
                      <el-radio v-for="dict in product_status" :key="dict.value" :label="dict.value"
                        >{{ dict.label }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>

                <!--是否推荐-->
                <el-col :span="12">
                  <el-form-item :label="$t('products.isRecommend')" prop="isRecommend">
                    <el-radio-group v-model="form.isRecommend">
                      <el-radio v-for="dict in sys_yes_no" :key="dict.value" :label="dict.value">
                        {{ $t(dict.type + '.' + dict.value) }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>

                <!--是否新品-->
                <el-col :span="12">
                  <el-form-item :label="$t('products.isNew')" prop="isNew">
                    <el-radio-group v-model="form.isNew">
                      <el-radio v-for="dict in sys_yes_no" :key="dict.value" :label="dict.value">
                        {{ $t(dict.type + '.' + dict.value) }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>

                <!--是否热销-->
                <el-col :span="12">
                  <el-form-item :label="$t('products.isHot')" prop="isHot">
                    <el-radio-group v-model="form.isHot">
                      <el-radio v-for="dict in sys_yes_no" :key="dict.value" :label="dict.value">
                        {{ $t(dict.type + '.' + dict.value) }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <!--分销佣金-->
                <el-col :span="12">
                  <el-form-item :label="$t('products.distributionCommission')" prop="sortOrder">
                    <el-input
                      v-model="form.distributionCommission"
                      :placeholder="
                        formatStr($t('input.please'), $t('products.distributionCommission'))
                      "
                    />
                  </el-form-item>
                </el-col>
                <!--排序-->
                <el-col :span="12">
                  <el-form-item :label="$t('products.sortOrder')" prop="sortOrder">
                    <el-input
                      v-model="form.sortOrder"
                      :placeholder="formatStr($t('input.please'), $t('products.sortOrder'))"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="规格库存" name="spec_stock">
              <div v-if="activeName === 'spec_stock'">
                <ProductSpecs v-model="form.productSpecsList" :product="form" />
                <!-- 规格表格组件 -->
                <SpecTable
                  v-if="hasValidSpecs"
                  ref="specTableRef"
                  v-model="form.productSkuList"
                  :specs="validSpecs"
                />
              </div>
            </el-tab-pane>
            <el-tab-pane label="商品描述" name="product_desc">
              <div class="big-flex big-gap-5">
                <div class="">
                  <Editors v-model="form.description" :height="600" />
                </div>
                <div style="width: 400px" class="container1">
                  <div class="phone-bg" v-html="form.description"></div>
                </div>
              </div>
            </el-tab-pane>
            <!--<el-tab-pane label="佣金" name="fourth">佣金</el-tab-pane>-->
          </el-tabs>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { addProducts, getProducts, updateProducts } from '@/api/system/products/Products'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import ProductSpecs from '@/components/ProductSpecs'
  import { formatStr } from '@/utils/ruoyi'
  import { computed, toRefs, watch } from 'vue'

  const { proxy } = getCurrentInstance()

  const specTableData = ref([])

  const { product_status, sys_yes_no } = proxy.useDict('product_status', 'sys_yes_no')
  const emits = defineEmits(['update:show', 'refreshList'])
  const activeName = ref('base_info')
  const specTableRef = ref(null)

  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const isLoading = ref(false)
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {
      firstRebate: 0,
      secondRebate: 0,
      vipPrice: 0,
      description: '',
      coverImage: '',
      productSkuList: [],
      productSpecsList: [],
      status: 'listing',
      isNew: 'Y',
      isHot: 'N',
      isRecommend: 'N'
    },
    rules: {
      coverImage: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('products.coverImage'), proxy.$t('not.empty')),
          trigger: ['change']
        }
      ],
      name: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('products.name'), proxy.$t('not.empty')),
          trigger: 'blur'
        }
      ],
      presalePrice: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('products.presalePrice'), proxy.$t('not.empty')),
          trigger: 'blur'
        }
      ],
      originalPrice: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('products.originalPrice'), proxy.$t('not.empty')),
          trigger: 'blur'
        }
      ],
      sellingPrice: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('products.sellingPrice'), proxy.$t('not.empty')),
          trigger: 'blur'
        }
      ],
      totalStock: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('products.totalStock'), proxy.$t('not.empty')),
          trigger: 'blur'
        }
      ],
      status: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('products.status'), proxy.$t('not.empty')),
          trigger: 'blur'
        }
      ],
      isRecommend: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('products.isRecommend'), proxy.$t('not.empty')),
          trigger: 'change'
        }
      ],
      isNew: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('products.isNew'), proxy.$t('not.empty')),
          trigger: 'change'
        }
      ],
      isHot: [
        {
          required: true,
          message: proxy.formatStr(proxy.$t('products.isHot'), proxy.$t('not.empty')),
          trigger: 'change'
        }
      ]
    }
  })

  const { form, rules } = toRefs(data)
  const loadingBtn = reactive({
    submit: false
  })

  // 获取有效的规格（有规格值的规格）
  const validSpecs = computed(() => {
    return form.value.productSpecsList.filter(
      (spec) => spec.specValues && spec.specValues.length > 0
    )
  })

  // 判断是否有任何有效规格
  const hasValidSpecs = computed(() => {
    return validSpecs.value.length > 0
  })

  // 监听规格变化，在提交时使用
  watch(
    specTableData,
    (newVal) => {
      if (hasValidSpecs.value) {
        // 更新表单中的规格数据
        form.value.specs = newVal
      } else {
        // 清空规格数据
        form.value.specs = []
      }
    },
    { deep: true }
  )

  /** 提交按钮 */
  async function submitForm() {
    if (specTableRef.value) {
      const isValid = await specTableRef.value.validate()
      if (!isValid) {
        return
      }
    }
    isLoading.value = true

    proxy.$refs['productsRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true

        // 深度克隆表单数据
        const formData = JSON.parse(JSON.stringify(form.value))

        // 处理图片字段：如果是数组，转换为字符串
        if (Array.isArray(formData.coverImage)) {
          formData.coverImage = formData.coverImage.join(',')
        }

        // 处理详情图片字段：如果是数组，转换为字符串
        if (Array.isArray(formData.detailImages)) {
          formData.detailImages = formData.detailImages.join(',')
        }

        // 转换规格值为字符串
        if (formData.productSpecsList && formData.productSpecsList.length > 0) {
          formData.productSpecsList.forEach((spec) => {
            if (Array.isArray(spec.specValues)) {
              spec.specValues = spec.specValues.join(',')
            }
          })
        }

        if (formData.id != null) {
          updateProducts(formData).then((response) => {
            proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
            emits('refreshList')
            open.value = false
            isLoading.value = false
          })
        } else {
          addProducts(formData).then((response) => {
            proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
            emits('refreshList')
            open.value = false
            isLoading.value = false
          })
        }
      }
    })
  }

  const init = () => {
    reset()
    if (props.id) {
      getProducts(props.id).then((response) => {
        let { productSkuList, productSpecsList } = response.data
        productSkuList.forEach((item) => {
          Object.keys(JSON.parse(item.specValues)).forEach((key) => {
            item[key] = JSON.parse(item.specValues)[key]
          })
        })
        splitSpecValues(productSpecsList)

        data.form = response.data
      })
    }
  }
  const splitSpecValues = (row) => {
    row.forEach((item) => {
      item.specValues = item.specValues.split(',')
    })
  }

  // 表单重置
  function reset() {
    form.value = {
      firstRebate: 0,
      secondRebate: 0,
      vipPrice: 0,
      productSpecsList: [],
      productSkuList: [],
      status: 'listing',
      isNew: 'Y',
      isHot: 'N',
      isRecommend: 'N'
    }
    isLoading.value = false
    activeName.value = 'base_info'
    proxy.resetForm('productsRef')
  }
</script>

<style>
  .phone-bg {
    width: 300px;
    border: 1px solid #ccc;
    height: 70vh;
    border-radius: 10px;
    overflow: hidden;
    overflow-y: auto;
    padding: 20px;
  }

  .container1 {
    display: flex;
    align-items: center;
    /* 垂直居中 */
    justify-content: center;
    /* 水平居中 */
    height: 100%;
    /* 确保容器有高度 */
    min-height: 800px;
    /* 设置最小高度，根据需要调整 */
  }
</style>
