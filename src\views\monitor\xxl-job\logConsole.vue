<template>
  <div>
    <div class="flex justify-around bg-cyan-800 text-white">
      <h1 class="font-bold py-5">{{ $t('log.execution.console') }}</h1>
      <div class="flex">
        <h3 class="cursor-pointer hover:bg-cyan-900 py-5 px-5 flex" @click="refresh">
          <el-icon class="">
            <Refresh />
          </el-icon>
          <p class="ml-1">{{ $t('action.refresh') }}</p>
        </h3>
        <h3 class="cursor-pointer hover:bg-cyan-900 py-5 px-5 flex" @click="close">
          <el-icon>
            <Close class="mt-1" />
          </el-icon>
          <p class="ml-1">{{ $t('action.close') }}</p>
        </h3>
      </div>
    </div>
  </div>
  <div class="mt-5 border-2 mx-5 rounded p-5">
    <p class="whitespace-pre-wrap" v-html="content"></p>
    <p class="text-green-800 mt-5"> [Load Log Finish]</p>
  </div>
</template>

<script setup>
  import { getCurrentInstance } from 'vue'

  const { proxy } = getCurrentInstance()
  let props = defineProps({
    content: {
      type: String,
      default: ''
    }
  })
  let emits = defineEmits(['refresh', 'close'])

  /**
   * 刷新
   */
  function refresh() {
    emits('refresh')
  }

  /**
   * 关闭
   */
  function close() {
    emits('close')
  }
</script>

<style scoped>
  :deep(.el-dialog .is-fullscreen) {
    background-color: #e9eef3 !important;
  }
</style>
