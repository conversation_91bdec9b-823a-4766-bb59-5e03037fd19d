<!--运行报表-->
<template>
  <div class="big-content">
    <h1>{{ $t('report.running') }}</h1>
    <el-row style="justify-content: space-between">
      <el-col :span="7">
        <div class="card" style="background-color: #56bdea">
          <div class="logo blue">
            <!--<i style="font-size: 39px" class="iconfont icon-shanjianzhi_qi"></i>-->
            <svg-icon class="mt-40" icon-class="qi" style="font-size: 45px" />
          </div>
          <div class="title">
            <div class="header">
              <h4>{{ $t('number.tasks') }}</h4>
              <h4 style="font-size: 23px">{{ dataInfo.jobInfoCount }}</h4>
            </div>
            <el-divider />
            <div class="header">
              <h4>{{ $t('tasks.running.center.scheduling') }}</h4>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :offset="1" :span="7">
        <div class="card" style="background-color: #e7a03c">
          <div class="logo orange">
            <!--<i style="font-size: 39px" class="iconfont icon-rili"></i>-->
            <svg-icon icon-class="rili" style="font-size: 45px" />
          </div>
          <div class="title">
            <div class="header">
              <h4>{{ $t('times.scheduling') }}</h4>
              <h4 style="font-size: 23px">{{ dataInfo.jobLogCount }}</h4>
            </div>
            <el-divider />
            <div class="header">
              <h4>{{ $t('scheduling.triggers.center') }}</h4>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="7" offset="2">
        <div class="card" style="background-color: #4aa361">
          <div class="logo green">
            <!--<i style="font-size: 39px" class="iconfont icon-xuanxiangka_fuzhi"></i>-->
            <svg-icon icon-class="xuanxiangka" style="font-size: 45px" />
          </div>
          <div class="title">
            <div class="header">
              <h4>{{ $t('executor.quantity') }}</h4>
              <h4 style="font-size: 23px">{{ dataInfo.executorCount }}</h4>
            </div>
            <el-divider />
            <div class="header">
              <h4>{{ $t('machines.executor.online') }}</h4>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <div class="content">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>{{ $t('report.scheduling') }}</h3>
            <div>
              <DateSelect :show-title="true" @change="handleChange"></DateSelect>
            </div>
            <div></div>
          </div>
        </template>

        <el-row>
          <el-col :span="16">
            <div ref="left" style="width: 100%; height: 400px"></div>
          </el-col>
          <el-col :span="8">
            <div ref="rigth" style="width: 100%; height: 400px"></div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { getCurrentInstance, onMounted, reactive, ref } from 'vue'
  //  按需引入 echarts
  import * as echarts from 'echarts'
  import { chartInfo } from '@/api/job'
  import DateSelect from '@/components/DateSelect/DateSelect'

  const { proxy } = getCurrentInstance()

  let data = reactive<{
    startDate: string
    endDate: string
  }>({ startDate: '', endDate: '' })
  let dataInfo = ref<{
    jobInfoCount: {
      type: number
      default: 0
    }
    jobLogCount: {
      type: number
      default: 0
    }
    executorCount: {
      type: number
      default: 0
    }
    triggerCountSucTotal: {
      type: number
      default: 0
    }
    triggerCountFailTotal: {
      type: number
      default: 0
    }
    triggerDayCountFailList: {
      type: Array<number>
      default: () => []
    }
    triggerCountRunningTotal: {
      type: Array<number>
      default: () => []
    }
    triggerDayList: {
      type: Array<number>
      default: () => []
    }
    triggerDayCountSucList: {
      type: Array<number>
      default: () => []
    }
    triggerDayCountRunningList: {
      type: Array<number>
      default: () => []
    }
  }>({})

  function handleChange(val) {
    if (!val) {
      timeInit()
      return
    }
    chartInfo(val).then((res) => {
      dataInfo.value = res.content
      init()
      pieChartInit()
    })
  }

  const left = ref() // 使用ref创建虚拟DOM引用，使用时用main.value
  const rigth = ref() // 使用ref创建虚拟DOM引用，使用时用main.value
  onMounted(() => {
    timeInit()
  })

  function timeInit() {
    const end = new Date()
    const start = new Date()
    start.setDate(start.getDate() - 7)
    start.setHours(0, 0, 0, 0)
    end.setHours(23, 59, 59, 0)
    let startDate = formatDate(start, 'YYYY-mm-dd HH:MM:SS')
    data.startDate = startDate
    let endDate = formatDate(end, 'YYYY-mm-dd HH:MM:SS')
    data.endDate = endDate
    chartInfo(data).then((res) => {
      dataInfo.value = res.content
      init()
      pieChartInit()
    })
  }

  /**
   * 日期格式转换
   * @param millisecond 毫秒
   * @param template 模板(可选)
   * @example formatDate(new Date(), "YYYY-mm-dd HH:MM:SS") => 2021-11-02 09:39:59
   */
  function formatDate(millisecond, template) {
    let res = ''
    try {
      let date = new Date(millisecond)
      let opt = {
        'Y+': date.getFullYear().toString(), // 年
        'm+': (date.getMonth() + 1).toString(), // 月
        'd+': date.getDate().toString(), // 日
        'H+': date.getHours().toString(), // 时
        'M+': date.getMinutes().toString(), // 分
        'S+': date.getSeconds().toString() // 秒
      }
      template = template || 'YYYY-mm-dd'
      for (let k in opt) {
        let ret = new RegExp('(' + k + ')').exec(template)
        if (ret) {
          template = template.replace(
            ret[1],
            ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0')
          )
        }
      }
      res = template
    } catch (error) {
      console.warn('ERROR formatDate', error)
    }
    return res
  }

  function init() {
    // 基于准备好的dom，初始化echarts实例
    let myChart = echarts.init(left.value)
    // 指定图表的配置项和数据
    let option = {
      title: {
        text: proxy.$t('chart.distribution.date')
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: [
          proxy.$t('status.success'),
          proxy.$t('status.failed'),
          proxy.$t('status.in.progress')
        ]
      },
      toolbox: {
        feature: {
          /*saveAsImage: {}*/
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: dataInfo.value.triggerDayList
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          name: proxy.$t('status.success'),
          type: 'line',
          stack: 'Total',
          areaStyle: { normal: {} },
          data: dataInfo.value.triggerDayCountSucList
        },
        {
          name: proxy.$t('status.failed'),
          type: 'line',
          stack: 'Total',
          label: {
            normal: {
              show: true,
              position: 'top'
            }
          },
          areaStyle: { normal: {} },
          data: dataInfo.value.triggerDayCountFailList
        },
        {
          name: proxy.$t('status.in.progress'),
          type: 'line',
          stack: 'Total',
          areaStyle: { normal: {} },
          data: dataInfo.value.triggerDayCountRunningList
        }
      ],
      color: ['#00A65A', '#c23632', '#F39C12']
    }
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option)
  }

  function pieChartInit() {
    let option = {
      title: {
        text: proxy.$t('chart.rate.success'),
        /*subtext: 'subtext',*/
        x: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b} : {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: [proxy.$t('status.success'), proxy.$t('status.failed'), proxy.$t('status.running')]
      },
      series: [
        {
          //name: '分布比例',
          type: 'pie',
          radius: '55%',
          center: ['50%', '60%'],
          data: [
            {
              name: proxy.$t('status.success'),
              value: dataInfo.value.triggerCountSucTotal
            },
            {
              name: proxy.$t('status.failed'),
              value: dataInfo.value.triggerCountFailTotal
            },
            {
              name: proxy.$t('status.running'),
              value: dataInfo.value.triggerCountRunningTotal
            }
          ],
          itemStyle: {
            emphasis: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ],
      color: ['#00A65A', '#c23632', '#F39C12']
    }
    let myChart = echarts.init(rigth.value)
    myChart.setOption(option)
  }
</script>
<style scoped>
  .btns {
    display: flex;
    flex-direction: column;
  }

  .btn-click {
    background-color: #3b86c6;
    color: white !important;
  }

  .btns > button:hover {
    background-color: #3b86c6;
    color: white !important;
  }

  .btns > button {
    margin-top: 5px;
    cursor: pointer;
    border-radius: 5px;
    color: #3b86c6;
    border: 0;
    font-weight: bold;
  }

  button {
    padding: 9px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .content {
    margin-top: 20px;
  }

  .header {
    padding: 5px 0 5px 10px;
    text-align: center;
  }

  .title {
    width: 100%;
  }

  .logo {
    width: 110px;

    height: 110px;
    border-bottom-left-radius: 5px;
    border-top-left-radius: 5px;
    text-align: center;
    line-height: 110px;
    color: #fff;
  }

  .blue {
    background-color: #4597bb;
  }

  .orange {
    background-color: #b98030;
  }

  .green {
    background-color: #3b824e;
  }

  .card {
    height: 110px;
    border-radius: 5px;

    box-shadow: 1px 1px 10px 1px #ccc;
    display: flex;
    color: white;
    width: 100%;
  }

  h1 {
    margin-bottom: 10px;
  }

  .big-content {
    padding: 15px;
  }

  * {
    margin: 0;
    padding: 0;
  }
</style>
