<template>
  <div>
    <div class="bg-sky-700 text-white">
      <div class="flex justify-around">
        <div class="flex">
          <h2 class="font-bold py-5 text-xl">WebIDE</h2>
          <div class="ml-5 bg-sky-900 py-5 pr-2"
            >【{{ props.info.glueTypeEnum }}】{{ props.info.jobInfo.jobDesc }}</div
          >
        </div>
        <div class="flex">
          <div class="hover:bg-sky-900 py-5 px-7 cursor-pointer">
            <el-dropdown @command="sourceChange">
              <span class="el-dropdown-link text-white"
                >{{ $t('rollback.version')
                }}<el-icon class="el-icon--right text-white">
                  <CaretBottom />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="item in props.info.jobLogGlues"
                    :command="{ guleSource: item.glueSource }"
                    >{{ props.info.glueTypeEnum }}：{{ item.glueRemark }}
                  </el-dropdown-item>

                  <el-dropdown-item v-if="props.info.jobLogGlues.length === 0">
                    {{ props.info.glueTypeEnum + '：' + props.info.jobInfo.glueRemark }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="hover:bg-sky-900 py-5 px-7 text-sm cursor-pointer" @click="save">
            <div class="flex">
              <el-icon class="pt-1 mr-2"><Select class="mt-0.5" /></el-icon>
              <p>{{ $t('action.save') }}</p>
            </div>
          </div>
          <div class="hover:bg-sky-900 py-5 px-7 text-sm cursor-pointer" @click="closeIde">
            <div class="flex">
              <el-icon class="pt-1 mr-2">
                <close class="mt-0.5" />
              </el-icon>
              <p>{{ $t('action.close') }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Codemirror
      ref="cmRef"
      v-model:value="props.info.jobInfo.glueSource"
      :options="cmOptions"
      :placeholder="$t('code.write')"
      :style="{
        height: 'calc(100vh - 70px)'
      }"
      border
      @change="change"
      @ready="onReady"
    />
  </div>
</template>

<script setup>
  import { getCurrentInstance, reactive, ref } from 'vue'
  import Codemirror from 'codemirror-editor-vue3'
  // theme
  import 'codemirror/theme/idea.css'
  import 'codemirror/keymap/sublime'
  import 'codemirror/mode/javascript/javascript.js'
  import 'codemirror/mode/xml/xml.js'
  import 'codemirror/mode/htmlmixed/htmlmixed.js'
  import 'codemirror/mode/css/css.js'
  import 'codemirror/mode/yaml/yaml.js'
  import 'codemirror/mode/sql/sql.js'
  import 'codemirror/mode/groovy/groovy.js'
  import 'codemirror/mode/shell/shell.js'
  import 'codemirror/mode/powershell/powershell.js'
  import 'codemirror/mode/php/php.js'
  import 'codemirror/mode/python/python.js'
  import 'codemirror/mode/markdown/markdown.js'
  import 'codemirror/addon/hint/show-hint.css'
  import 'codemirror/addon/hint/show-hint.js'
  import 'codemirror/addon/hint/javascript-hint.js'
  import 'codemirror/addon/hint/xml-hint.js'
  import 'codemirror/addon/hint/css-hint.js'
  import 'codemirror/addon/hint/html-hint.js'
  import 'codemirror/addon/hint/sql-hint.js'
  import 'codemirror/addon/hint/anyword-hint.js'
  import 'codemirror/addon/lint/lint.css'
  import 'codemirror/addon/lint/lint.js'
  import 'codemirror/addon/lint/json-lint'
  import 'codemirror/addon/selection/active-line'
  import 'codemirror/addon/lint/javascript-lint.js'
  import 'codemirror/addon/fold/foldcode.js'
  import 'codemirror/addon/fold/foldgutter.js'
  import 'codemirror/addon/fold/foldgutter.css'
  import 'codemirror/addon/fold/brace-fold.js'
  import 'codemirror/addon/fold/xml-fold.js'
  import 'codemirror/addon/fold/comment-fold.js'
  import 'codemirror/addon/fold/markdown-fold.js'
  import 'codemirror/addon/fold/indent-fold.js'
  import 'codemirror/addon/edit/closebrackets.js'
  import 'codemirror/addon/edit/closetag.js'
  import 'codemirror/addon/edit/matchtags.js'
  import 'codemirror/addon/edit/matchbrackets.js'
  import 'codemirror/addon/selection/active-line.js'
  import 'codemirror/addon/search/jump-to-line.js'
  import 'codemirror/addon/dialog/dialog.js'
  import 'codemirror/addon/dialog/dialog.css'
  import 'codemirror/addon/search/searchcursor.js'
  import 'codemirror/addon/search/search.js'
  import 'codemirror/addon/display/autorefresh.js'
  import 'codemirror/addon/selection/mark-selection.js'
  import 'codemirror/addon/search/match-highlighter.js'

  const { proxy } = getCurrentInstance()

  const cmRef = ref()

  const props = defineProps({
    info: {
      glueTypeEnum: String,
      jobInfo: {
        glueType: String,
        glueSource: String
      },
      modelType: String,
      jobLogGlues: Array
    },
    // 数据
    options: {
      type: Array,
      default: null
    },
    // 当前的值
    value: [Number, String, Array]
  })

  const emits = defineEmits(['save', 'close'])

  const onReady = (cm) => {
    cmRef.value?.refresh()
  }

  const cmOptions = reactive({
    merge: true,
    autoRefresh: true,
    mode: props.info.modelType, // Language mode
    theme: 'idea', // Theme
    lineNumbers: true, // Show line number
    smartIndent: true, // Smart indent
    indentUnit: 2, // The smart indent unit is 2 spaces in length
    styleActiveLine: true, // Display the style of the selected row
    tabSize: 4,
    autocorrect: true, // 自动更正
    spellcheck: true, // 拼写检查
    lint: true, // 检查格式
    matchBrackets: true, //括号匹配
    autoCloseBrackets: true, // 在键入时将自动关闭括号和引号
    matchTags: { bothTags: true }, // 将突出显示光标周围的标签
    foldGutter: true, // 可将对象折叠，与下面的gutters一起使用
    gutters: ['CodeMirror-lint-markers', 'CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
    highlightSelectionMatches: {
      minChars: 2,
      style: 'matchhighlight',
      showToken: true
    }
  })

  function closeIde() {
    emits('close')
  }

  function sourceChange(val) {
    props.info.jobInfo.glueSource = val.guleSource
  }

  function save() {
    let jobInfo = props.info.jobInfo
    let data = {
      id: jobInfo.id,
      glueSource: jobInfo.glueSource
    }
    emits('save', data)
  }

  function change(val) {
    console.log(val)
  }
</script>

<style scoped>
  :deep(.CodeMirror-line) {
    font-size: 17px !important;
  }

  :deep(.CodeMirror-linenumber) {
    font-size: 17px !important;
  }
</style>
