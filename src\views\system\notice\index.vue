<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="getList"
      @refresh="refresh"
      @search="getList"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:notice:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:notice:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:notice:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <right-toolbar @queryTable="getList"></right-toolbar>
    </el-row>

    <!--<el-table v-loading="loading" :data="noticeList" @selection-change="handleSelectionChange" height="540px">-->
    <art-table
      v-loading="loading"
      :data="noticeList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="getList"
      row-key="noticeId"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column :label="$t('number.serial')" align="center" prop="noticeId" width="100" />
      <el-table-column
        :label="$t('title.notice')"
        :show-overflow-tooltip="true"
        align="center"
        prop="noticeTitle"
      >
        <template #default="{ row }">
          <el-link type="primary" @click="handleDetail(row)">{{ row.noticeTitle }}</el-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('type.notice')" align="center" prop="noticeType" width="100">
        <template #default="scope">
          <dict-tag :options="sys_notice_type" :value="scope.row.noticeType" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('status')" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="sys_notice_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('creator')" align="center" prop="createBy" width="100" />
      <el-table-column :label="$t('time.creation')" align="center" prop="createTime" width="100">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['system:notice:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            >{{ $t('action.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:notice:remove']"
            icon="Delete"
            link
            type="danger"
            @click="handleDelete(scope.row)"
            >{{ $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!--<pagination-->
    <!--   v-show="total > 0"-->
    <!--   :total="total"-->
    <!--   v-model:page="queryParams.pageNum"-->
    <!--   v-model:limit="queryParams.pageSize"-->
    <!--   @pagination="getList"-->
    <!--/>-->

    <!-- 添加或修改公告对话框 -->
    <add v-model:show="open" :title="title" :id="noticeId" @refreshList="refreshList" />

    <!-- 公告详情对话框 -->
    <detail v-model:show="detailShow" :title="title" :id="noticeId" />
  </div>
</template>

<script name="Notice" setup>
  import { delNotice, listNotice } from '@/api/system/notice'
  import { getCurrentInstance } from 'vue'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import add from './components/add.vue'
  import detail from './components/detail.vue'

  const { proxy } = getCurrentInstance()

  const { sys_notice_status, sys_notice_type } = proxy.useDict(
    'sys_notice_status',
    'sys_notice_type'
  )

  const noticeList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const noticeId = ref('')
  const loading = ref(true)
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    fromKey: 'notice',
    fromName: 'notice',
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      noticeTitle: undefined,
      createBy: undefined,
      status: undefined
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询公告列表 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    listNotice(queryParams.value).then(({ data }) => {
      noticeList.value = data.records
      total.value = data.totalRow
      loading.value = false
    })
  }



  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.noticeId)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  /** 新增按钮操作 */
  function handleAdd() {
    noticeId.value = ''
    title.value = proxy.$t('notice.add')
    open.value = true
  }

  /**修改按钮操作 */
  function handleUpdate(row) {
    noticeId.value = row.noticeId || ids.value
    title.value = proxy.$t('notice.modify')
    open.value = true
  }

  /** 查看详情操作 */
  function handleDetail(row) {
    noticeId.value = row.noticeId
    title.value = proxy.$t('notice.detail')
    detailShow.value = true
  }



  /** 删除按钮操作 */
  function handleDelete(row) {
    const noticeIds = row.noticeId || ids.value
    proxy.$modal
      .confirm(
        proxy.$t('confirm.delete.notice.number') + noticeIds + proxy.$t('item.data.question')
      )
      .then(function () {
        return delNotice(noticeIds)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  const refreshList = () => {
    getList()
  }

  const refresh = () => {
    getList()
  }

  getList()
</script>
