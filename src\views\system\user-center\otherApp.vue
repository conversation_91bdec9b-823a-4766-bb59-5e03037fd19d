<template>
  <div>
    <el-table :data="auths" style="width: 100%; height: 100%">
      <el-table-column
        :label="$t('number.serial')"
        align="center"
        type="index"
        width="130"
      ></el-table-column>
      <el-table-column
        :label="$t('platform.account.bind')"
        :show-overflow-tooltip="true"
        align="center"
        prop="source"
        width="190"
      >
        <template #default="scope">
          <dict-tag :options="other_login" :value="scope.row.source" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('avatar')" align="center" prop="avatar">
        <template #default="scope" style="width: 100%; margin: 0 auto">
          <el-image :src="scope.row.avatar" class="rounded" style="width: 45px; height: 45px" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('account.system')" align="center" prop="userName" width="160" />
      <el-table-column :label="$t('time.binding')" align="center" prop="createTime" width="120" />
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
        width="120"
      >
        <template #default="scope">
          <el-button
            :icon="Delete"
            circle
            type="danger"
            @click="unlockAuth(scope.$index, scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <div id="git-user-binding">
      <h4 class="provider-desc big-mt-5">{{ $t('application.third.party.bind') }}</h4>
      <div class="big-flex big-pb-5">
        <img
          :title="$t('gitee')"
          class="other-login big-mt-5"
          src="../../../assets/source/gitee.png"
          @click="authUrl('gitee')"
        />
        <img
          :title="$t('wechat.enterprise')"
          class="other-login big-mt-5"
          src="../../../assets/source/wechatEnterprise.png"
          @click="authUrl('wechatEnterprise')"
        />
        <img
          class="other-login big-mt-5"
          src="../../../assets/source/qq.png"
          title="QQ"
          @click="authUrl('qq')"
        />
        <img
          class="other-login big-mt-5"
          src="../../../assets/source/dingTalk.png"
          title="QQ"
          @click="authUrl('dingtalk')"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance } from 'vue'
  import { authBinding, authUnlock } from '@/api/system/auth'
  import { Delete } from '@element-plus/icons-vue'

  const { proxy } = getCurrentInstance()

  const { other_login } = proxy.useDict('other_login')

  let props = defineProps({
    auths: { type: Array, required: true, default: [{}] }
  })

  function unlockAuth(index, row) {
    proxy.$modal
      .confirm(proxy.$t('remove.confirm') + row.source + proxy.$t('binding.account.question'))
      .then(function () {
        return authUnlock(row.authId)
      })
      .then(() => {
        props.auths.splice(index, 1)
        proxy.$modal.msgSuccess(proxy.$t('unbind.successful'))
      })
      .catch(() => {})
  }

  function authUrl(source) {
    authBinding(source).then((res) => {
      //打开新的页面
      top.location.href = res.msg
    })
  }
</script>

<style scoped>
  .other-login {
    height: 30px;
    cursor: pointer;
    margin-left: 10px;
  }
</style>
