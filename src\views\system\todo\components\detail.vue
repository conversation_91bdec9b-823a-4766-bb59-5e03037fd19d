<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-row>
          <!--待办标题-->
          <el-col :span="12">
            <el-form-item :label="$t('todo.title')" prop="title">
              {{ form.title }}
            </el-form-item>
          </el-col>
          <!--待办日期-->
          <el-col :span="12">
            <el-form-item :label="$t('todo.date')" prop="date">
              {{ form.date }}
            </el-form-item>
          </el-col>
          <!--备注信息-->
          <el-col :span="12">
            <el-form-item :label="$t('todo.notes')" prop="notes">
              {{ form.notes }}
            </el-form-item>
          </el-col>
          <!--金额-->
          <el-col :span="12">
            <el-form-item :label="$t('todo.amount')" prop="amount">
              {{ form.amount }}
            </el-form-item>
          </el-col>
          <!--是否提醒（0不需要 1需要）-->
          <el-col :span="12">
            <el-form-item :label="$t('todo.needReminder')" prop="needReminder">
              {{ form.needReminder }}
            </el-form-item>
          </el-col>
        </el-row>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'

  import { computed, toRefs } from 'vue'
  import { getTodo } from '@/api/system/todo/Todo'

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    data.form = {}
    if (props.id) {
      getTodo(props.id).then((response) => {
        data.form = response.data
      })
    }
  }
</script>
