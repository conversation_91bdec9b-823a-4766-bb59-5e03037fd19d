<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-row>
          <!--礼簿标题-->
          <el-col :span="12">
            <el-form-item :label="$t('giftBooks.title')" prop="title">
              {{ form.title }}
            </el-form-item>
          </el-col>
          <!--礼簿类型(字典：gift_book_type)-->
          <el-col :span="12">
            <el-form-item :label="$t('giftBooks.type')" prop="type">
              <dict-tag :options="gift_book_type" :value="form.type" />
            </el-form-item>
          </el-col>
          <!--用户ID-->
          <el-col :span="12">
            <el-form-item :label="$t('giftBooks.userId')" prop="userId">
              {{ form.userId }}
            </el-form-item>
          </el-col>
        </el-row>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'

  const { gift_book_type } = proxy.useDict('gift_book_type')
  import { computed, toRefs } from 'vue'
  import { getGiftBooks } from '@/api/system/tGiftBooks/TGiftBooks'

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    data.form = {}
    if (props.id) {
      getGiftBooks(props.id).then((response) => {
        data.form = response.data
      })
    }
  }
</script>
