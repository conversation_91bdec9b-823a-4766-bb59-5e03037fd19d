import request from '@/utils/request'

// 查询高级查询列表
export function listHigthQuery(data) {
  return request({
    url: '/system/higthQuery/list',
    method: 'post',
    data
  })
}

// 查询高级查询详细
export function getHigthQuery(id) {
  return request({
    url: '/system/higthQuery/' + id,
    method: 'get'
  })
}

// 新增高级查询
export function addHigthQuery(data) {
  return request({
    url: '/system/higthQuery',
    method: 'post',
    data: data
  })
}

// 修改高级查询
export function updateHigthQuery(data) {
  return request({
    url: '/system/higthQuery',
    method: 'put',
    data: data
  })
}

// 删除高级查询
export function delHigthQuery(id) {
  return request({
    url: '/system/higthQuery/' + id,
    method: 'delete'
  })
}
