<template>
  <div class="">
    <el-row>
      <el-col :span="12" class="card-box">
        <el-card>
          <template #header>
            <Cpu style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">CPU</span></template
          >
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%">
              <thead>
                <tr>
                  <th class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('attribute') }}</div>
                  </th>
                  <th class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('value') }}</div>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('cores.number') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.cpu" class="cell">{{ server.cpu.cpuNum }}</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('rate.utilization.user') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.cpu" class="cell">{{ server.cpu.used }}%</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('rate.utilization.system') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.cpu" class="cell">{{ server.cpu.sys }}%</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('rate.idle.current') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.cpu" class="cell">{{ server.cpu.free }}%</div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12" class="card-box">
        <el-card>
          <template #header>
            <Tickets style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('memory') }}</span></template
          >
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%">
              <thead>
                <tr>
                  <th class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('attribute') }}</div>
                  </th>
                  <th class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('memory') }}</div>
                  </th>
                  <th class="el-table__cell is-leaf">
                    <div class="cell">JVM</div>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('memory.total') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.mem" class="cell">{{ server.mem.total }}G</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.jvm" class="cell">{{ server.jvm.total }}M</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('memory.used') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.mem" class="cell">{{ server.mem.used }}G</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.jvm" class="cell">{{ server.jvm.used }}M</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('memory.remaining') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.mem" class="cell">{{ server.mem.free }}G</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.jvm" class="cell">{{ server.jvm.free }}M</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('rate.utilization') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div
                      v-if="server.mem"
                      :class="{ 'text-danger': server.mem.usage > 80 }"
                      class="cell"
                    >
                      {{ server.mem.usage }}%
                    </div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div
                      v-if="server.jvm"
                      :class="{ 'text-danger': server.jvm.usage > 80 }"
                      class="cell"
                    >
                      {{ server.jvm.usage }}%
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <template #header>
            <Monitor style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('information.server') }}</span></template
          >
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%">
              <tbody>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('name.server') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.sys" class="cell">{{ server.sys.computerName }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('system.operating') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.sys" class="cell">{{ server.sys.osName }}</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('ip.server') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.sys" class="cell">{{ server.sys.computerIp }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('architecture.system') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.sys" class="cell">{{ server.sys.osArch }}</div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <template #header>
            <CoffeeCup style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('information.vm.java') }}</span></template
          >
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%; table-layout: fixed">
              <tbody>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('name.java') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.jvm" class="cell">{{ server.jvm.name }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('java.version') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.jvm" class="cell">{{ server.jvm.version }}</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('time.startup') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.jvm" class="cell">{{ server.jvm.startTime }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('duration.running') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="server.jvm" class="cell">{{ server.jvm.runTime }}</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf" colspan="1">
                    <div class="cell">{{ $t('path.installation') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf" colspan="3">
                    <div v-if="server.jvm" class="cell">{{ server.jvm.home }}</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf" colspan="1">
                    <div class="cell">{{ $t('path.project') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf" colspan="3">
                    <div v-if="server.sys" class="cell">{{ server.sys.userDir }}</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf" colspan="1">
                    <div class="cell">{{ $t('parameters.runtime') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf" colspan="3">
                    <div v-if="server.jvm" class="cell">{{ server.jvm.inputArgs }}</div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <template #header>
            <MessageBox style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('disk.status') }}</span></template
          >
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%">
              <thead>
                <tr>
                  <th class="el-table__cell el-table__cell is-leaf">
                    <div class="cell">{{ $t('path.disk.symbol') }}</div>
                  </th>
                  <th class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('system.file') }}</div>
                  </th>
                  <th class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('type.disk.symbol') }}</div>
                  </th>
                  <th class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('size.total') }}</div>
                  </th>
                  <th class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('size.available') }}</div>
                  </th>
                  <th class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('size.used') }}</div>
                  </th>
                  <th class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('percentage.used') }}</div>
                  </th>
                </tr>
              </thead>
              <tbody v-if="server.sysFiles">
                <tr v-for="(sysFile, index) in server.sysFiles" :key="index">
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ sysFile.dirName }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ sysFile.sysTypeName }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ sysFile.typeName }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ sysFile.total }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ sysFile.free }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ sysFile.used }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div :class="{ 'text-danger': sysFile.usage > 80 }" class="cell"
                      >{{ sysFile.usage }}%</div
                    >
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
  import { getCurrentInstance } from 'vue'
  import { getServer } from '@/api/monitor/server'

  const { proxy } = getCurrentInstance()

  const server = ref([])

  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    proxy.$modal.loading(proxy.$t('data.monitoring.service.loading'))
    getServer().then((response) => {
      server.value = response.data
      proxy.$modal.closeLoading()
    })
  }

  getList()
</script>
