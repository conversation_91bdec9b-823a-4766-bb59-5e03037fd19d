<template>
  <Drawer v-model="open" size="60%" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #content>

      <DrawerGroup>
        <Activiti v-if="flow" :process-id="form.taskId" />
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { computed, toRefs } from 'vue'
  import { getWork } from '@/api/system/work/Work'

  const { work_status, work_type } = proxy.useDict('work_status', 'work_type')
  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,

    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      flow.value = false
      emits('update:show', val)
    }
  })
  const data = reactive({
    flow: false,
    form: {}
  })

  const { form, flow } = toRefs(data)

  const init = () => {
    reset()
    if (props.id) {
      getWork(props.id).then((response) => {
        data.form = response.data
        flow.value = true
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      title: null,
      busiKey: null,
      type: null,
      sender: null,
      receiver: null,
      status: null
    }
    proxy.resetForm('workRef')
  }
</script>
