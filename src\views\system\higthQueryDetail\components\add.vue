<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>
          {{ $t('intl.drawer.basicInfo') }}
        </template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="higthQueryDetailRef" :model="form" :rules="rules">
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('condition')" prop="andOr">
                <el-select
                  v-model="form.andOr"
                  :placeholder="$t('condition.select')"
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in and_or"
                    :key="dict.value"
                    :label="$t(dict.type + '.' + dict.value)"
                    :value="dict.value"
                  >
                    <el-tag :type="dict.elTagType">
                      {{ $t(dict.type + '.' + dict.value) }}
                    </el-tag>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('sort')" prop="sortNum">
                <el-input v-model="form.sortNum" :placeholder="$t('sort.input')" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('disabled.question')" prop="isDisable">
                <el-select
                  v-model="form.isDisable"
                  :placeholder="$t('disable.select')"
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in sys_yes_no"
                    :key="dict.value"
                    :label="$t(dict.type + '.' + dict.value)"
                    :value="dict.value"
                  >
                    <el-tag :type="dict.elTagType">
                      {{ $t(dict.type + '.' + dict.value) }}
                    </el-tag>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('alias.table')" prop="ruleAlias">
                <el-input v-model="form.ruleAlias" :placeholder="$t('alias.table.input')" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('name.label')" prop="label">
                <!--<el-input v-model="form.label" placeholder="请输入标签名"/>-->
                <el-select
                  v-model="form.label"
                  :placeholder="$t('name.column.select')"
                  :reserve-keyword="false"
                  allow-create
                  default-first-option
                  filterable
                  @change="labelChange"
                >
                  <el-option
                    v-for="item in data.columnList"
                    :key="item.id"
                    :label="item.columnComment"
                    :value="item.columnComment"
                  >
                    <span style="float: left">{{ item.columnComment }}</span>
                    <span
                      style="float: right; color: var(--el-text-color-secondary); font-size: 13px"
                    >
                      {{ item.columnName }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('default.question')" prop="isDefault">
                <el-select
                  v-model="form.isDefault"
                  :placeholder="$t('default.select')"
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in sys_yes_no"
                    :key="dict.value"
                    :label="$t(dict.type + '.' + dict.value)"
                    :value="dict.value"
                  >
                    <el-tag :type="dict.elTagType">
                      {{ $t(dict.type + '.' + dict.value) }}
                    </el-tag>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('name.column')" prop="column">
                <el-input v-model="form.column" :placeholder="$t('name.column.input')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('type')" prop="type">
                <el-select
                  v-model="form.type"
                  :placeholder="$t('type.select')"
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in higth_query_type"
                    :key="dict.value"
                    :label="$t(dict.type + '.' + dict.value)"
                    :value="dict.value"
                  >
                    <el-tag :type="dict.elTagType">
                      {{ $t(dict.type + '.' + dict.value) }}
                    </el-tag>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('condition')" prop="condition">
                <el-select
                  v-model="form.condition"
                  :placeholder="$t('condition.select')"
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in higth_query_condition"
                    :key="dict.value"
                    :label="$t(dict.type + '.' + dict.value)"
                    :value="dict.value"
                  >
                    <el-tag :type="dict.elTagType">
                      {{ $t(dict.type + '.' + dict.value) }}
                    </el-tag>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('type.component')" prop="commonType">
                <el-select
                  v-model="form.commonType"
                  :placeholder="$t('type.component.select')"
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in hight_query_compoent"
                    :key="dict.value"
                    :label="$t(dict.type + '.' + dict.value)"
                    :value="dict.value"
                  >
                    <el-tag :type="dict.elTagType">
                      {{ $t(dict.type + '.' + dict.value) }}
                    </el-tag>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col v-if="form.commonType === 'dict'" :span="12">
              <el-form-item :label="$t('dictionary')" prop="dictKey">
                <el-select
                  v-model="form.dictKey"
                  :placeholder="$t('dictionary.select')"
                  clearable
                  filterable
                  :filter-method="filterDictOptions"
                >
                  <el-option
                    v-for="dict in filteredDictOptions"
                    :key="dict.dictType"
                    :label="dict.dictName"
                    :value="dict.dictType"
                  >
                    <span style="float: left">{{ dict.dictName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{
                      dict.dictType
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col v-if="form.commonType === 'common'" :span="12">
              <el-form-item :label="$t('name.table')" prop="tableName">
                <el-select
                  v-model="form.tableName"
                  :placeholder="$t('dictionary.select')"
                  clearable
                  filterable
                  remote
                  :remote-method="handleTableNameSelect"
                  @change="changeTableName"
                >
                  <el-option
                    v-for="dict in tableNameOptions"
                    :key="dict.tableName"
                    :label="dict.tableName"
                    :value="dict.tableName"
                  >
                    <span style="float: left">{{ dict.tableName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{
                      dict.tableComment
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col v-if="form.commonType === 'common'" :span="12">
              <el-form-item :label="$t('target_field')" prop="targetField">
                <el-select
                  v-model="form.targetField"
                  :placeholder="$t('select.please')"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="dict in allFieldOptions"
                    :key="dict.columnName"
                    :label="dict.columnName"
                    :value="dict.columnName"
                  >
                    <span style="float: left">{{ dict.columnName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{
                      dict.columnComment
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col v-if="form.commonType === 'common'" :span="12">
              <el-form-item :label="$t('where_field')" prop="whereField">
                <el-select
                  v-model="form.whereField"
                  :placeholder="$t('select.please')"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="dict in allFieldOptions"
                    :key="dict.columnName"
                    :label="dict.columnName"
                    :value="dict.columnName"
                  >
                    <span style="float: left">{{ dict.columnName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{
                      dict.columnComment
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <!--<el-col :span="12">-->
            <!--  <el-form-item label="业务主键" prop="busiKey">-->
            <!--    <el-input v-model="form.busiKey" placeholder="请输入业务主键"/>-->
            <!--  </el-form-item>-->
            <!--</el-col>-->
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, getCurrentInstance, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'
  import { getColumnList, listTables, queryByBusinessName } from '@/api/tool/gen'
  import {
    addHigthQueryDetail,
    getHigthQueryDetail,
    updateHigthQueryDetail
  } from '@/api/system/higthQueryDetail/HigthQueryDetail'
  import { optionselect as getDictOptionselect } from '@/api/system/dict/type'

  const { proxy } = getCurrentInstance()

  const dictOptions = ref([])
  const filteredDictOptions = ref([])
  const tableNameOptions = ref([])
  const allFieldOptions = ref([])

  const { higth_query_condition, and_or, sys_yes_no, hight_query_compoent, higth_query_type } =
    proxy.useDict(
      'higth_query_condition',
      'and_or',
      'sys_yes_no',
      'hight_query_compoent',
      'higth_query_type'
    )
  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    busiKey: {
      type: String,
      default: ''
    },
    sortMaxCode: {
      type: Number,
      default: 0
    },
    id: {
      type: String,
      default: ''
    }
  })
  const changeTableName = (item) => {
    if (item === '') {
      allFieldOptions.value = []
      return
    }
    getColumnList({
      tableName: item
    }).then((res) => {
      allFieldOptions.value = res.data
    })
  }
  const handleTableNameSelect = (item) => {
    listTables({
      tableName: item
    }).then((res) => {
      tableNameOptions.value = res.rows
    })
  }

  // 字典过滤方法 - 支持按label和value搜索
  const filterDictOptions = (query) => {
    if (!query) {
      filteredDictOptions.value = dictOptions.value
      return
    }

    const lowerQuery = query.toLowerCase()
    filteredDictOptions.value = dictOptions.value.filter((dict) => {
      // 同时搜索字典名称(label)和字典类型(value)
      const nameMatch = dict.dictName.toLowerCase().includes(lowerQuery)
      const typeMatch = dict.dictType.toLowerCase().includes(lowerQuery)
      return nameMatch || typeMatch
    })
  }

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {},
    columnList: [],
    rules: {
      andOr: [{ required: true, message: proxy.$t('condition.empty'), trigger: 'change' }],
      dictKey: [{ required: true, message: proxy.$t('dictionary.empty'), trigger: 'change' }],
      sortNum: [{ required: true, message: proxy.$t('sort.empty'), trigger: 'blur' }],
      isDisable: [{ required: true, message: proxy.$t('disabled.empty'), trigger: 'change' }],
      column: [{ required: true, message: proxy.$t('name.column.empty'), trigger: 'blur' }],
      label: [{ required: true, message: proxy.$t('label.name.empty'), trigger: 'blur' }],
      type: [{ required: true, message: proxy.$t('type.empty'), trigger: 'change' }],
      condition: [{ required: true, message: proxy.$t('condition.empty'), trigger: 'change' }],
      commonType: [
        { required: true, message: proxy.$t('component.type.empty'), trigger: 'change' }
      ],
      busiKey: [{ required: true, message: proxy.$t('key.business.empty'), trigger: 'blur' }],
      tableName: [{ required: true, message: proxy.$t('table.name.empty'), trigger: 'change' }],
      targetField: [{ required: true, message: proxy.$t('field.target.empty'), trigger: 'change' }],
      whereField: [{ required: true, message: proxy.$t('field.where.empty'), trigger: 'change' }]
    }
  })

  const { form, rules } = toRefs(data)
  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['higthQueryDetailRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true

        if (form.value.id != null) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateHigthQueryDetail(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }
  const add = () => {
    form.value.busiKey = props.busiKey
    addHigthQueryDetail(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getHigthQueryDetail(props.id).then((response) => {
        data.form = response.data
        if (data.form.commonType === 'common' && data.form.tableName) {
          changeTableName(data.form.tableName)
        }
      })
    }
    /** 查询字典下拉列表 */
    getDictOptionselect().then((response) => {
      dictOptions.value = response.data
      // 初始化过滤后的选项
      filteredDictOptions.value = response.data
    })
    queryByBusinessName({ businessName: props.busiKey }).then((res) => {
      data.columnList = res.data
    })
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      andOr: 'and',
      sortNum: props.sortMaxCode,
      isDisable: 'N',
      ruleAlias: null,
      column: null,
      isDefault: 'Y',
      label: null,
      type: 'string',
      condition: '=',
      commonType: 'input',
      busiKey: null
    }
    proxy.resetForm('higthQueryDetailRef')
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }
  const labelChange = (val) => {
    const filterElement = data.columnList.filter((el) => el.columnComment === val)[0]
    if (filterElement) {
      form.value.column = filterElement.columnName
    }
  }
  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }
</script>
