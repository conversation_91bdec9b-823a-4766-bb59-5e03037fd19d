import request from '@/utils/request'

// 查询查询方案列表
export function listQueryCase(data) {
  return request({
    url: '/system/queryCase/list',
    method: 'post',
    data
  })
}

export function queryMyQueryCase(data) {
  return request({
    url: '/system/queryCase/queryMyQueryCase',
    method: 'post',
    data
  })
}

// 查询查询方案详细
export function getQueryCase(id) {
  return request({
    url: '/system/queryCase/' + id,
    method: 'get'
  })
}

// 新增查询方案
export function addQueryCase(data) {
  return request({
    url: '/system/queryCase',
    method: 'post',
    data: data
  })
}

// 修改查询方案
export function updateQueryCase(data) {
  return request({
    url: '/system/queryCase',
    method: 'put',
    data: data
  })
}

// 删除查询方案
export function delQueryCase(id) {
  return request({
    url: '/system/queryCase/' + id,
    method: 'delete'
  })
}

export function commonQuery(params) {
  return request({
    url: '/common/commonQuery',
    method: 'get',
    params
  })
}
