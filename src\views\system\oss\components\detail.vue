<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('sysOss.fileName')" prop="fileName">
              {{ form.fileName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('sysOss.originalName')" prop="originalName">
              {{ form.originalName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('sysOss.fileSuffix')" prop="fileSuffix">
              {{ form.fileSuffix }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('sysOss.url')" prop="url">
              <image-preview
                v-if="isImg(form.fileSuffix)"
                :height="50"
                :src="form.url"
                :width="50"
              />
              <span v-else> {{ form.fileSuffix }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('sysOss.service')" prop="service">
              {{ form.service }}
            </el-form-item>
          </el-col>
        </el-row>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { isImg } from '@/utils/ruoyi'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'

  import { computed, toRefs } from 'vue'
  import { getOss } from '@/api/system/oss/Oss'

  const { proxy } = getCurrentInstance()

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    if (props.id) {
      getOss(props.id).then((response) => {
        data.form = response.data
      })
    }
  }
</script>
