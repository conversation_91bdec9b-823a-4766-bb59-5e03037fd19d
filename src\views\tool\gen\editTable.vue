<template>
  <el-card>
    <el-tabs v-model="activeName">
      <el-tab-pane :label="$t('information.basic')" name="basic">
        <basic-info-form ref="basicInfo" :info="info" />
      </el-tab-pane>
      <el-tab-pane :label="$t('information.field')" name="columnInfo">
        <el-table ref="dragTable" :data="columns" :max-height="tableHeight" row-key="columnId">
          <el-table-column :label="$t('number.serial')" min-width="5%" type="index" />
          <el-table-column
            :label="$t('name.column.field')"
            :show-overflow-tooltip="true"
            min-width="10%"
            prop="columnName"
          />
          <el-table-column :label="$t('description.field')" min-width="10%">
            <template #default="scope">
              <el-input v-model="scope.row.columnComment"></el-input>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('type.physical')"
            :show-overflow-tooltip="true"
            min-width="10%"
            prop="columnType"
          />
          <el-table-column :label="$t('type.java')" min-width="11%">
            <template #default="scope">
              <el-select v-model="scope.row.javaType">
                <el-option label="Long" value="Long" />
                <el-option label="String" value="String" />
                <el-option label="Integer" value="Integer" />
                <el-option label="Double" value="Double" />
                <el-option label="BigDecimal" value="BigDecimal" />
                <el-option label="Date" value="Date" />
                <el-option label="Boolean" value="Boolean" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column :label="$t('property.java')" min-width="10%">
            <template #default="scope">
              <el-input v-model="scope.row.javaField"></el-input>
            </template>
          </el-table-column>

          <el-table-column :label="$t('action.insert')" min-width="5%">
            <template #default="scope">
              <el-checkbox
                v-model="scope.row.isInsert"
                false-label="0"
                true-label="1"
              ></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column :label="$t('action.edit')" min-width="5%">
            <template #default="scope">
              <el-checkbox v-model="scope.row.isEdit" false-label="0" true-label="1"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column :label="$t('list')" min-width="5%">
            <template #default="scope">
              <el-checkbox v-model="scope.row.isList" false-label="0" true-label="1"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column :label="$t('action.search')" min-width="5%">
            <template #default="scope">
              <el-checkbox v-model="scope.row.isQuery" false-label="0" true-label="1"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column :label="$t('method.search')" min-width="10%">
            <template #default="scope">
              <el-select v-model="scope.row.queryType">
                <el-option label="=" value="EQ" />
                <el-option label="!=" value="NE" />
                <el-option label=">" value="GT" />
                <el-option label=">=" value="GTE" />
                <el-option label="<" value="LT" />
                <el-option label="<=" value="LTE" />
                <el-option label="LIKE" value="LIKE" />
                <el-option label="BETWEEN" value="BETWEEN" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column :label="$t('required')" min-width="5%">
            <template #default="scope">
              <el-checkbox
                v-model="scope.row.isRequired"
                false-label="0"
                true-label="1"
              ></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column :label="$t('type.display')" min-width="12%">
            <template #default="scope">
              <el-select v-model="scope.row.htmlType">
                <el-option :label="$t('box.text')" value="input" />
                <el-option :label="$t('department')" value="dept" />
                <el-option :label="$t('user')" value="user" />
                <el-option :label="$t('textarea')" value="textarea" />
                <el-option :label="$t('dropdown')" value="select" />
                <el-option :label="$t('button.radio')" value="radio" />
                <el-option :label="$t('checkbox')" value="checkbox" />
                <el-option :label="$t('control.date')" value="datetime" />
                <el-option :label="$t('upload.image')" value="imageUpload" />
                <el-option :label="$t('upload.file')" value="fileUpload" />
                <el-option :label="$t('control.text.rich')" value="editor" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column :label="$t('type.dictionary')" min-width="12%">
            <template #default="scope">
              <el-select
                v-model="scope.row.dictType"
                :placeholder="$t('select.please')"
                clearable
                filterable
              >
                <el-option
                  v-for="dict in dictOptions"
                  :key="dict.dictType"
                  :label="dict.dictName"
                  :value="dict.dictType"
                >
                  <span style="float: left">{{ dict.dictName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{
                    dict.dictType
                  }}</span>
                </el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane :label="$t('information.generation')" name="genInfo">
        <gen-info-form ref="genInfo" :info="info" :tables="tables" />
      </el-tab-pane>
    </el-tabs>
    <el-form label-width="100px">
      <div style="text-align: center; margin-left: -100px; margin-top: 10px">
        <el-button type="primary" @click="submitForm()">{{ $t('intl.btn.submit') }}</el-button>
        <el-button @click="close()">{{ $t('action.return') }}</el-button>
      </div>
    </el-form>
  </el-card>
</template>

<script name="GenEdit" setup>
  import { getCurrentInstance } from 'vue'
  import { getGenTable, updateGenTable } from '@/api/tool/gen'
  import { optionselect as getDictOptionselect } from '@/api/system/dict/type'
  import basicInfoForm from './basicInfoForm'
  import genInfoForm from './genInfoForm'
  import { useWorktabStore } from '@/store/modules/worktab'

  const { proxy } = getCurrentInstance()

  const router = useRouter()
  const route = useRoute()
  const store = useWorktabStore()
  const activeName = ref('columnInfo')
  const tableHeight = ref(document.documentElement.scrollHeight - 245 + 'px')
  const tables = ref([])
  const columns = ref([])
  const dictOptions = ref([])
  const info = ref({})

  /** 提交按钮 */
  function submitForm() {
    const basicForm = proxy.$refs.basicInfo.$refs.basicInfoForm
    const genForm = proxy.$refs.genInfo.$refs.genInfoForm
    Promise.all([basicForm, genForm].map(getFormPromise)).then((res) => {
      const validateResult = res.every((item) => !!item)
      if (validateResult) {
        const genTable = Object.assign({}, info.value)
        genTable.columns = columns.value
        genTable.params = {
          treeCode: info.value.treeCode,
          treeName: info.value.treeName,
          treeParentCode: info.value.treeParentCode,
          parentMenuId: info.value.parentMenuId
        }
        updateGenTable(genTable).then((res) => {
          proxy.$modal.msgSuccess(res.msg)
          if (res.code === 200) {
            close()
          }
        })
      } else {
        proxy.$modal.msgError(proxy.$t('validation.form.fail'))
      }
    })
  }

  function getFormPromise(form) {
    return new Promise((resolve) => {
      form.validate((res) => {
        resolve(res)
      })
    })
  }

  function close() {
    // store.remove(`/tool/gen-edit/index/${route.params.tableId}`, router,'/tool/gen')
    // const obj = { path: '/tool/gen', query: { t: Date.now(), pageNum: route.query.pageNum } }
    proxy.$tab.closeOpenPage()
  }

  ;(() => {
    const tableId = route.params && route.params.tableId
    if (tableId) {
      // 获取表详细信息
      getGenTable(tableId).then((res) => {
        columns.value = res.data.rows
        info.value = { ...res.data.info, ...res.data.custom }
        tables.value = res.data.tables
      })
      /** 查询字典下拉列表 */
      getDictOptionselect().then((response) => {
        dictOptions.value = response.data
      })
    }
  })()
</script>
