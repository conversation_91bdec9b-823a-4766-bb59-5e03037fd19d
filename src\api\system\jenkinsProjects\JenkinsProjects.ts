import request from '@/utils/request'

// 查询jenkins项目列表
export function listJenkinsProjects(data) {
  return request({
    url: '/system/jenkinsProjects/list',
    method: 'post',
    data
  })
}

// 查询jenkins项目详细
export function getJenkinsProjects(id) {
  return request({
    url: '/system/jenkinsProjects/' + id,
    method: 'get'
  })
}

// 新增jenkins项目
export function addJenkinsProjects(data) {
  return request({
    url: '/system/jenkinsProjects',
    method: 'post',
    data: data
  })
}

// 修改jenkins项目
export function updateJenkinsProjects(data) {
  return request({
    url: '/system/jenkinsProjects',
    method: 'put',
    data: data
  })
}

// 删除jenkins项目
export function delJenkinsProjects(id) {
  return request({
    url: '/system/jenkinsProjects/' + id,
    method: 'delete'
  })
}
