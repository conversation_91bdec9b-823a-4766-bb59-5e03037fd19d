<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-row>
          <!--所属礼簿ID-->
          <el-col :span="12">
            <el-form-item :label="$t('giftRecords.bookId')" prop="bookId">
              {{ form.bookId }}
            </el-form-item>
          </el-col>
          <!--送礼人姓名-->
          <el-col :span="12">
            <el-form-item :label="$t('giftRecords.name')" prop="name">
              {{ form.name }}
            </el-form-item>
          </el-col>
          <!--礼金金额-->
          <el-col :span="12">
            <el-form-item :label="$t('giftRecords.amount')" prop="amount">
              {{ form.amount }}
            </el-form-item>
          </el-col>
          <!--礼金类型(字典：gift_money_type)-->
          <el-col :span="12">
            <el-form-item :label="$t('giftRecords.type')" prop="type">
              <!--<dict-tag :options="" :value="form.type" />-->
            </el-form-item>
          </el-col>
          <!--收礼日期-->
          <el-col :span="12">
            <el-form-item :label="$t('giftRecords.date')" prop="date">
              {{ form.date }}
            </el-form-item>
          </el-col>
        </el-row>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'

  import { computed, toRefs } from 'vue'
  import { getGiftRecords } from '@/api/system/tGiftRecords/TGiftRecords'

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    data.form = {}
    if (props.id) {
      getGiftRecords(props.id).then((response) => {
        data.form = response.data
      })
    }
  }
</script>
