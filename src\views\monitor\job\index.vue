<template>
  <div class="page-content">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
      <el-form-item :label="$t('name.task')" prop="jobName">
        <el-input
          v-model="queryParams.jobName"
          :placeholder="$t('name.task.input')"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('name.group.task')" prop="jobGroup">
        <el-select
          v-model="queryParams.jobGroup"
          :placeholder="$t('name.group.task.select')"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in sys_job_group"
            :key="dict.value"
            :label="$t(dict.type + '.' + dict.value)"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('status.task')" prop="status">
        <el-select
          v-model="queryParams.status"
          :placeholder="$t('status.task.select')"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in sys_job_status"
            :key="dict.value"
            :label="$t(dict.type + '.' + dict.value)"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery"
          >{{ $t('action.search') }}
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('action.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:job:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:job:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:job:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:job:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:job:query']"
          icon="Operation"
          plain
          type="info"
          @click="handleJobLog"
          >{{ $t('log') }}
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!--<el-table v-loading="loading" :data="jobList" @selection-change="handleSelectionChange" height="540px">-->
    <art-table
      v-loading="loading"
      :data="jobList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="getList"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column :label="$t('number.task')" align="center" prop="jobId" width="100" />
      <el-table-column
        :label="$t('name.task')"
        :show-overflow-tooltip="true"
        align="center"
        prop="jobName"
      />
      <el-table-column :label="$t('name.group.task')" align="center" prop="jobGroup">
        <template #default="scope">
          <dict-tag :options="sys_job_group" :value="scope.row.jobGroup" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('string.target.call')"
        :show-overflow-tooltip="true"
        align="center"
        prop="invokeTarget"
      />
      <el-table-column
        :label="$t('cron.expression')"
        :show-overflow-tooltip="true"
        align="center"
        prop="cronExpression"
      />
      <el-table-column :label="$t('status')" align="center">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
        width="200"
      >
        <template #default="scope">
          <el-tooltip :content="$t('action.modify')" placement="top">
            <el-button
              v-hasPermi="['monitor:job:edit']"
              icon="Edit"
              link
              type="primary"
              @click="handleUpdate(scope.row)"
            ></el-button>
          </el-tooltip>
          <el-tooltip :content="$t('action.delete')" placement="top">
            <el-button
              v-hasPermi="['monitor:job:remove']"
              icon="Delete"
              link
              type="primary"
              @click="handleDelete(scope.row)"
            ></el-button>
          </el-tooltip>
          <el-tooltip :content="$t('execute.once')" placement="top">
            <el-button
              v-hasPermi="['monitor:job:changeStatus']"
              icon="CaretRight"
              link
              type="primary"
              @click="handleRun(scope.row)"
            ></el-button>
          </el-tooltip>
          <el-tooltip :content="$t('details.task')" placement="top">
            <el-button
              v-hasPermi="['monitor:job:query']"
              icon="View"
              link
              type="primary"
              @click="handleView(scope.row)"
            ></el-button>
          </el-tooltip>
          <el-tooltip :content="$t('log.scheduling')" placement="top">
            <el-button
              v-hasPermi="['monitor:job:query']"
              icon="Operation"
              link
              type="primary"
              @click="handleJobLog(scope.row)"
            ></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改定时任务对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="820px">
      <el-form ref="jobRef" :model="form" :rules="rules">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('name.task')" prop="jobName">
              <el-input v-model="form.jobName" :placeholder="$t('name.task.input')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('group.task')" prop="jobGroup">
              <el-select v-model="form.jobGroup" :placeholder="$t('select.please')">
                <el-option
                  v-for="dict in sys_job_group"
                  :key="dict.value"
                  :label="$t(dict.type + '.' + dict.value)"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="invokeTarget">
              <template #label>
                <span
                  >{{ $t('method.call')
                  }}<el-tooltip placement="top">
                    <template #content>
                      <div
                        >{{ $t('example.call.bean') }}<br />{{ $t('example.call.class') }}<br />{{
                          $t('description.parameter')
                        }}</div
                      >
                    </template>
                    <el-icon><question-filled /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-input v-model="form.invokeTarget" :placeholder="$t('call.target.string')" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('expression.cron')" prop="cronExpression">
              <el-input v-model="form.cronExpression" :placeholder="$t('expression.cron.input')">
                <template #append>
                  <el-button type="primary" @click="handleShowCron"
                    >{{ $t('expression.generate') }}<i class="el-icon-time el-icon--right"></i>
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="form.jobId !== undefined" :span="24">
            <el-form-item :label="$t('status')">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in sys_job_status" :key="dict.value" :value="dict.value"
                  >{{ $t(dict.type + '.' + dict.value) }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('strategy.execution')" prop="misfirePolicy">
              <el-radio-group v-model="form.misfirePolicy">
                <el-radio-button value="1">{{ $t('execute.immediately') }}</el-radio-button>
                <el-radio-button value="2">{{ $t('execute.once') }}</el-radio-button>
                <el-radio-button value="3">{{ $t('execution.abort') }}</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('concurrent')" prop="concurrent">
              <el-radio-group v-model="form.concurrent">
                <el-radio-button value="0">{{ $t('action.allow') }}</el-radio-button>
                <el-radio-button value="1">{{ $t('action.prohibited') }}</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('action.confirm') }}</el-button>
          <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="openCron"
      :title="$t('generator.cron.expression')"
      append-to-body
      destroy-on-close
    >
      <crontab
        ref="crontabRef"
        :expression="expression"
        @fill="crontabFill"
        @hide="openCron = false"
      ></crontab>
    </el-dialog>

    <!-- 任务日志详细 -->
    <el-dialog v-model="openView" :title="$t('details.task')" append-to-body width="700px">
      <el-form :model="form">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('number.task')">{{ form.jobId }}</el-form-item>
            <el-form-item :label="$t('task.name')">{{ form.jobName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('group.task')">{{ jobGroupFormat(form) }}</el-form-item>
            <el-form-item :label="$t('time.creation')">{{ form.createTime }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('expression.cron')">{{ form.cronExpression }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('time.execution.next')"
              >{{ parseTime(form.nextValidTime) }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('method.target.call')">{{ form.invokeTarget }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('status.task')">
              <div v-if="form.status == 0">{{ $t('status.normal') }}</div>
              <div v-else-if="form.status == 1">{{ $t('action.pause') }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('concurrent.question')">
              <div v-if="form.concurrent == 0">{{ $t('action.allow') }}</div>
              <div v-else-if="form.concurrent == 1">{{ $t('action.prohibited') }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('strategy.execution')">
              <div v-if="form.misfirePolicy == 0">{{ $t('policy.default') }}</div>
              <div v-else-if="form.misfirePolicy == 1">{{ $t('execute.immediately') }}</div>
              <div v-else-if="form.misfirePolicy == 2">{{ $t('execute.once') }}</div>
              <div v-else-if="form.misfirePolicy == 3">{{ $t('execution.abort') }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="openView = false">{{ $t('action.close') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="Job" setup>
  import { getCurrentInstance } from 'vue'
  import {
    addJob,
    changeJobStatus,
    delJob,
    getJob,
    listJob,
    runJob,
    updateJob
  } from '@/api/monitor/job'
  import Crontab from '@/components/Crontab'

  const { proxy } = getCurrentInstance()

  const router = useRouter()
  const { sys_job_group, sys_job_status } = proxy.useDict('sys_job_group', 'sys_job_status')

  const jobList = ref([])
  const open = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')
  const openView = ref(false)
  const openCron = ref(false)
  const expression = ref('')

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      jobName: undefined,
      jobGroup: undefined,
      status: undefined
    },
    rules: {
      jobName: [{ required: true, message: proxy.$t('name.task.empty'), trigger: 'blur' }],
      invokeTarget: [
        { required: true, message: proxy.$t('string.target.call.empty'), trigger: 'blur' }
      ],
      cronExpression: [
        { required: true, message: proxy.$t('expression.cron.empty'), trigger: 'change' }
      ]
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  /** 查询定时任务列表 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    listJob(queryParams.value).then((response) => {
      jobList.value = response.rows
      total.value = response.total
      loading.value = false
    })
  }

  /** 任务组名字典翻译 */
  function jobGroupFormat(row, column) {
    return proxy.selectDictLabel(sys_job_group.value, row.jobGroup)
  }

  /** 取消按钮 */
  function cancel() {
    open.value = false
    reset()
  }

  /** 表单重置 */
  function reset() {
    form.value = {
      jobId: undefined,
      jobName: undefined,
      jobGroup: undefined,
      invokeTarget: undefined,
      cronExpression: undefined,
      misfirePolicy: 1,
      concurrent: 1,
      status: '0'
    }
    proxy.resetForm('jobRef')
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef')
    handleQuery()
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.jobId)
    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  // 更多操作触发
  function handleCommand(command, row) {
    switch (command) {
      case 'handleRun':
        handleRun(row)
        break
      case 'handleView':
        handleView(row)
        break
      case 'handleJobLog':
        handleJobLog(row)
        break
      default:
        break
    }
  }

  // 任务状态修改
  function handleStatusChange(row) {
    let text = row.status === '0' ? proxy.$t('action.enable') : proxy.$t('action.disable')
    proxy.$modal
      .confirm(proxy.$t('confirm.action') + text + '""' + row.jobName + proxy.$t('task.question'))
      .then(function () {
        return changeJobStatus(row.jobId, row.status)
      })
      .then(() => {
        proxy.$modal.msgSuccess(text + proxy.$t('status.success'))
      })
      .catch(function () {
        row.status = row.status === '0' ? '1' : '0'
      })
  }

  /* 立即执行一次 */
  function handleRun(row) {
    proxy.$modal
      .confirm(proxy.$t('confirm.execute.once') + row.jobName + proxy.$t('task.question'))
      .then(function () {
        return runJob(row.jobId, row.jobGroup)
      })
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('execution.success'))
      })
      .catch(() => {})
  }

  /** 任务详细信息 */
  function handleView(row) {
    getJob(row.jobId).then((response) => {
      form.value = response.data
      openView.value = true
    })
  }

  /** cron表达式按钮操作 */
  function handleShowCron() {
    expression.value = form.value.cronExpression
    openCron.value = true
  }

  /** 确定后回传值 */
  function crontabFill(value) {
    form.value.cronExpression = value
  }

  /** 任务日志列表查询 */
  function handleJobLog(row) {
    const jobId = row.jobId || 0
    router.push('/monitor/job-log/index/' + jobId)
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset()
    open.value = true
    title.value = proxy.$t('task.add')
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset()
    const jobId = row.jobId || ids.value
    getJob(jobId).then((response) => {
      form.value = response.data
      open.value = true
      title.value = proxy.$t('task.modify')
    })
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['jobRef'].validate((valid) => {
      if (valid) {
        if (form.value.jobId != undefined) {
          updateJob(form.value).then((response) => {
            proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
            open.value = false
            getList()
          })
        } else {
          addJob(form.value).then((response) => {
            proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
            open.value = false
            getList()
          })
        }
      }
    })
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const jobIds = row.jobId || ids.value
    proxy.$modal
      .confirm(
        proxy.$t('confirm.delete.task.scheduled.number') + jobIds + proxy.$t('item.data.question')
      )
      .then(function () {
        return delJob(jobIds)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'monitor/job/export',
      {
        ...queryParams.value
      },
      `job_${new Date().getTime()}.xlsx`
    )
  }

  getList()
</script>
