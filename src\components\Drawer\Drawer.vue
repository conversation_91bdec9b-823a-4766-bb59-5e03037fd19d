<template>
  <div>
    <el-drawer
      v-model="open"
      :close-on-click-modal="false"
      :show-close="false"
      :size="size"
      @close="emits('close')"
      @closed="emits('closed')"
      @open="emits('open')"
      @opened="emits('opened')"
      @open-auto-focus="emits('open-auto-focus')"
      @close-auto-focus="emits('close-auto-focus')"
    >
      <template #header="{ close, titleId, titleClass }">
        <div class="font-bold text-lg">
          <slot name="title"></slot>
        </div>
        <div>
          <slot name="btn_group"></slot>
        </div>
        <button class="drawer-close-btn big-cursor-pointer" link @click="closeDrawer"
          >{{ $t('action.close') }}
        </button>
      </template>
      <slot name="content"></slot>
    </el-drawer>
  </div>
</template>

<script setup>
  import { getCurrentInstance, ref, watchEffect } from 'vue'

  const { proxy } = getCurrentInstance()

  const emits = defineEmits([
    'update:modelValue',
    'open',
    'opened',
    'close',
    'closed',
    'open-auto-focus',
    'close-auto-focus'
  ])
  const props = defineProps({
    modelValue: Boolean,
    size: {
      type: String,
      default: '50%'
    }
  })

  let open = ref(false)
  watchEffect(() => {
    open.value = props.modelValue
  })

  const closeDrawer = () => {
    emits('update:modelValue', false)
  }
</script>

<style lang="scss" scoped>
  .drawer-close-btn {
    font-size: 15px;
    background-color: #f64041;
    color: #fff;
    font-weight: bold;
    letter-spacing: 7px;
    text-indent: 7px;
    padding: 20px 2px;
    border-radius: 5px;
    position: absolute;
    top: 40%;
    left: -28px;
    writing-mode: vertical-rl;
    border: 1px solid #fff;
  }

  :deep(.el-drawer) {
    overflow: visible;
  }

  :deep(.el-drawer__header) {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 0;
  }

  :deep(.el-drawer__body) {
    padding: 0 var(--el-drawer-padding-primary);
    padding-top: 10px;
    padding-bottom: var(--el-drawer-padding-primary);
  }
</style>
