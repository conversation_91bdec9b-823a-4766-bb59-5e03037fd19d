<template>
  <Drawer v-model="open" @open="init" :size="600">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm">
          {{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="configRef" :model="form" :rules="rules" label-width="120px">
          <el-row>
            <!-- 参数名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.parameter')" prop="configName">
                <el-input v-model="form.configName" :placeholder="$t('name.parameter.input')" />
              </el-form-item>
            </el-col>

            <!-- 参数键名 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.key.parameter')" prop="configKey">
                <el-input v-model="form.configKey" :placeholder="$t('name.key.parameter.input')" />
              </el-form-item>
            </el-col>

            <!-- 参数键值 -->
            <el-col :span="12">
              <el-form-item :label="$t('key.value.parameter')" prop="configValue">
                <el-input
                  v-model="form.configValue"
                  :placeholder="$t('value.key.parameter.input')"
                />
              </el-form-item>
            </el-col>

            <!-- 系统内置 -->
            <el-col :span="12">
              <el-form-item :label="$t('built.in.system')" prop="configType">
                <el-radio-group v-model="form.configType">
                  <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">
                    {{ $t(dict.type + '.' + dict.value) }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <!-- 备注 -->
            <el-col :span="24">
              <el-form-item :label="$t('remarks')" prop="remark">
                <el-input
                  v-model="form.remark"
                  :placeholder="$t('content.input')"
                  type="textarea"
                  :rows="3"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { addConfig, getConfig, updateConfig } from '@/api/system/config'

  const { proxy } = getCurrentInstance()
  const { sys_yes_no } = proxy.useDict('sys_yes_no')

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {},
    rules: {
      configName: [{ required: true, message: proxy.$t('name.parameter.empty'), trigger: 'blur' }],
      configKey: [
        { required: true, message: proxy.$t('name.key.parameter.empty'), trigger: 'blur' }
      ],
      configValue: [
        { required: true, message: proxy.$t('key.value.parameter.empty'), trigger: 'blur' }
      ]
    }
  })

  const { form, rules } = toRefs(data)

  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['configRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true
        if (form.value.configId !== undefined) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateConfig(form.value)
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const add = () => {
    addConfig(form.value)
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getConfig(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      configId: undefined,
      configName: undefined,
      configKey: undefined,
      configValue: undefined,
      configType: 'Y',
      remark: undefined
    }
    proxy.resetForm('configRef')
  }
</script>

<style scoped>
  /* 组件样式 */
</style>
