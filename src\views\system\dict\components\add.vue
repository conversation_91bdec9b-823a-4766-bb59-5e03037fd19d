<template>
  <Drawer v-model="open" @open="init" :size="600">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm">
          {{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="dictRef" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <!-- 字典名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.dictionary')" prop="dictName">
                <el-input v-model="form.dictName" :placeholder="$t('name.dictionary.input')" />
              </el-form-item>
            </el-col>

            <!-- 字典类型 -->
            <el-col :span="12">
              <el-form-item :label="$t('type.dictionary')" prop="dictType">
                <el-input v-model="form.dictType" :placeholder="$t('dictionary.type.input')" />
              </el-form-item>
            </el-col>

            <!-- 状态 -->
            <el-col :span="12">
              <el-form-item :label="$t('status')" prop="status">
                <el-radio-group v-model="form.status">
                  <el-radio
                    v-for="dict in sys_normal_disable"
                    :key="dict.value"
                    :value="dict.value"
                  >
                    {{ $t(dict.type + '.' + dict.value) }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <!-- 备注 -->
            <el-col :span="24">
              <el-form-item :label="$t('remarks')" prop="remark">
                <el-input
                  v-model="form.remark"
                  :placeholder="$t('content.input')"
                  type="textarea"
                  :rows="3"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { addType, getType, updateType } from '@/api/system/dict/type'

  const { proxy } = getCurrentInstance()
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {},
    rules: {
      dictName: [{ required: true, message: proxy.$t('dictionary.name.empty'), trigger: 'blur' }],
      dictType: [{ required: true, message: proxy.$t('type.dictionary.empty'), trigger: 'blur' }]
    }
  })

  const { form, rules } = toRefs(data)

  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['dictRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true
        if (form.value.dictId !== undefined) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateType(form.value)
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const add = () => {
    addType(form.value)
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getType(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      dictId: undefined,
      dictName: undefined,
      dictType: undefined,
      status: '0',
      remark: undefined
    }
    proxy.resetForm('dictRef')
  }
</script>

<style scoped>
  /* 组件样式 */
</style>
