<template>
  <el-form ref="userRef" :model="form" :rules="rules" label-position="top">
    <el-form-item :label="$t('nickname.user')" prop="nickName">
      <el-input v-model="form.nickName" maxlength="30" />
    </el-form-item>
    <el-form-item :label="$t('number.mobile')" prop="phonenumber">
      <el-input v-model="form.phonenumber" maxlength="11" />
    </el-form-item>
    <el-form-item :label="$t('email')" prop="email">
      <el-input v-model="form.email" maxlength="50" />
    </el-form-item>
    <el-form-item :label="$t('gender')">
      <el-radio-group v-model="form.sex">
        <el-radio value="0">{{ $t('gender.male') }}</el-radio>
        <el-radio value="1">{{ $t('gender.female') }}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="TOTP">
      <template #label>
        <span class="inline-flex items-center">
          TOTP
          <el-tooltip
            :content="$t('verification.code.login.enable')"
            class="item"
            effect="dark"
            placement="top"
          >
            <el-icon class="big-ml-1" size="15">
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </span>
      </template>
      <el-switch
        :active-text="$t('action.close')"
        :inactive-text="$t('action.enable')"
        :model-value="form.enableTotp"
        active-value="Y"
        inactive-value="N"
        inline-prompt
        @change="enableTotpChange"
      />
    </el-form-item>
    <div v-if="form.enableTotp === 'Y' && oldEnableTotp !== 'Y'" class="big-mb-8">
      <el-form-item :label="$t('code.qr.totp')">
        <el-row>
          <el-col>
            <div>
              <el-image
                :preview-src-list="[data.qrCodeImage]"
                :src="data.qrCodeImage"
                style="width: 150px; height: 150px"
              />
            </div>
          </el-col>
          <el-col>
            <div class="big-cursor-pointer big-flex big-items-center">
              <div> {{ data.totpSecret }}</div>
              <div class="big-ml-2">
                <el-tooltip :content="$t('key.copy')" placement="top">
                  <el-icon size="15" @click="copySecret">
                    <CopyDocument />
                  </el-icon>
                </el-tooltip>
              </div>
              <div>
                <el-icon v-if="!data.eye" class="big-ml-2" size="15" @click="eyeShow">
                  <View />
                </el-icon>
                <el-icon v-else class="big-ml-2" size="15" @click="eyeShow">
                  <Hide />
                </el-icon>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item :label="$t('code.verification')" prop="verificationCode">
        <el-input v-model="form.verificationCode" maxlength="6" type="number" />
      </el-form-item>
    </div>
    <el-form-item>
      <el-button type="primary" @click="submit">{{ $t('action.save') }}</el-button>
      <el-button type="danger" @click="close">{{ $t('action.close') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
  import { getCurrentInstance } from 'vue'
  import { updateUserProfile } from '@/api/system/user'
  import { setupDevice } from '@/api/system/totp'
  import { useUserStore } from '@/store/modules/user'

  const { proxy } = getCurrentInstance()

  const userStore = useUserStore()

  const props = defineProps({
    user: {
      type: Object
    }
  })

  const form = ref({
    enableTotp: 'N'
  })

  const data = reactive({
    eye: false
  })
  const rules = ref({
    nickName: [{ required: true, message: proxy.$t('nickname.user.empty'), trigger: 'blur' }],
    verificationCode: [{ required: true, message: proxy.$t('totp.code.empty'), trigger: 'blur' }],
    email: [
      { required: true, message: proxy.$t('address.email.empty'), trigger: 'blur' },
      {
        type: 'email',
        message: proxy.$t('address.email.correct'),
        trigger: ['blur', 'change']
      }
    ],
    phonenumber: [
      { required: true, message: proxy.$t('number.mobile.empty'), trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: proxy.$t('number.mobile.correct'), trigger: 'blur' }
    ]
  })

  /** 提交按钮 */
  function submit() {
    proxy.$refs.userRef.validate((valid) => {
      if (valid) {
        updateUserProfile(form.value).then((response) => {
          let userInfo = userStore.getUserInfo
          proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))

          userInfo.phonenumber = form.value.phonenumber
          userInfo.nickName = form.value.nickName
          userInfo.email = form.value.email
          oldEnableTotp.value = form.value.enableTotp
          form.value.totpSecret = null
        })
      }
    })
  }

  const close = () => {
    proxy.$tab.closePage()
  }

  const oldEnableTotp = ref('N')

  // 回显当前登录用户信息
  watch(
    () => props.user,
    (user) => {
      if (user) {
        form.value = {
          nickName: user.nickName,
          phonenumber: user.phonenumber,
          email: user.email,
          sex: user.sex,
          enableTotp: user.enableTotp ?? 'N'
        }
        oldEnableTotp.value = user.enableTotp ?? 'N'
      }
    },
    { immediate: true }
  )

  /** TOTP 开关状态变更 */
  const enableTotpChange = (val) => {
    const initialValue = form.value.enableTotp // 存储初始值
    if (val === 'Y') {
      proxy.$modal
        .confirm(proxy.$t('verification.code.login.enable'), proxy.$t('tip'), {
          confirmButtonText: proxy.$t('action.confirm'),
          cancelButtonText: proxy.$t('action.cancel'),
          type: 'warning'
        })
        .then(() => {
          setupDevice().then((response) => {
            // 秘钥需要用*替换下
            form.value.totpSecret = response.data.secret
            data.totpSecret = maskString(response.data.secret)
            data.qrCodeImage = response.data.qrCodeImage
            form.value.enableTotp = 'Y'
          })
        })
        .catch(() => {
          form.value.enableTotp = initialValue // 恢复初始值
        })
    } else {
      proxy.$modal
        .confirm(proxy.$t('close.continue'), proxy.$t('tip'), {
          confirmButtonText: proxy.$t('action.confirm'),
          cancelButtonText: proxy.$t('action.cancel'),
          type: 'warning'
        })
        .then(() => {
          form.value.enableTotp = 'N'
        })
        .catch(() => {
          form.value.enableTotp = initialValue // 恢复初始值
        })
    }
  }

  const copySecret = () => {
    //   拷贝秘钥到粘贴板
    navigator.clipboard.writeText(form.value.totpSecret).then(() => {
      proxy.$modal.msgSuccess(proxy.$t('copy.success'))
    })
  }
  const maskString = (str, visibleChars = 4) => {
    const totalLength = str.length

    if (totalLength <= visibleChars * 2) {
      return str // 如果字符串太短，则不进行替换
    }

    const start = str.substring(0, visibleChars) // 获取前部分
    const end = str.substring(totalLength - visibleChars) // 获取后部分
    const maskedSection = '*'.repeat(totalLength - visibleChars * 2) // 用星号替换中间部分
    return `${start}${maskedSection}${end}`
  }
  const eyeShow = () => {
    if (data.eye) {
      data.eye = false
      data.totpSecret = maskString(data.totpSecret)
    } else {
      data.eye = true
      data.totpSecret = form.value.totpSecret
    }
  }
</script>
