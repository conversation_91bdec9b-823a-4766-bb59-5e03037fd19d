<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ac5e7402-2dde-4f2a-b962-fd6c2fac2a63" name="Changes" comment="重构" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev-icon-ruoyi" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="30JwN1QEOh6zOm16hj4FJ0UShUY" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev-icon&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.standard&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.stylelint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.standard&quot;: &quot;&quot;,
    &quot;node.js.selected.package.stylelint&quot;: &quot;D:\\code\\art-design-pro\\node_modules\\stylelint&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;pnpm&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;D:\\code\\art-design-pro\\node_modules\\prettier&quot;,
    &quot;run.code.analysis.last.selected.profile&quot;: &quot;pProject Default&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\code\\art-design-pro\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-WS-252.23892.361" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ac5e7402-2dde-4f2a-b962-fd6c2fac2a63" name="Changes" comment="" />
      <created>1753358939937</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753358939937</updated>
      <workItem from="1753358941037" duration="3967000" />
      <workItem from="1753362950193" duration="7251000" />
      <workItem from="1753444344795" duration="782000" />
      <workItem from="1753445140787" duration="371000" />
      <workItem from="1753445521009" duration="17375000" />
      <workItem from="1753491544321" duration="4569000" />
      <workItem from="1753548151253" duration="215000" />
      <workItem from="1753605939974" duration="11469000" />
      <workItem from="1753702536470" duration="5954000" />
      <workItem from="1753878115403" duration="126000" />
      <workItem from="1753878258852" duration="601000" />
    </task>
    <task id="LOCAL-00001" summary="重构">
      <option name="closed" value="true" />
      <created>1753370207656</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753370207656</updated>
    </task>
    <task id="LOCAL-00002" summary="重构">
      <option name="closed" value="true" />
      <created>1753450368601</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753450368601</updated>
    </task>
    <task id="LOCAL-00003" summary="重构">
      <option name="closed" value="true" />
      <created>1753456502149</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753456502149</updated>
    </task>
    <task id="LOCAL-00004" summary="重构">
      <option name="closed" value="true" />
      <created>1753457183304</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753457183304</updated>
    </task>
    <task id="LOCAL-00005" summary="重构">
      <option name="closed" value="true" />
      <created>1753458631271</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753458631271</updated>
    </task>
    <task id="LOCAL-00006" summary="重构">
      <option name="closed" value="true" />
      <created>1753460429740</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753460429740</updated>
    </task>
    <task id="LOCAL-00007" summary="重构">
      <option name="closed" value="true" />
      <created>1753464039070</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753464039070</updated>
    </task>
    <task id="LOCAL-00008" summary="重构">
      <option name="closed" value="true" />
      <created>1753494810698</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753494810698</updated>
    </task>
    <task id="LOCAL-00009" summary="重构">
      <option name="closed" value="true" />
      <created>1753495433461</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753495433461</updated>
    </task>
    <task id="LOCAL-00010" summary="重构">
      <option name="closed" value="true" />
      <created>1753611385869</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753611385869</updated>
    </task>
    <task id="LOCAL-00011" summary="重构">
      <option name="closed" value="true" />
      <created>1753618871577</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753618871577</updated>
    </task>
    <task id="LOCAL-00012" summary="重构">
      <option name="closed" value="true" />
      <created>1753625348212</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753625348212</updated>
    </task>
    <task id="LOCAL-00013" summary="重构">
      <option name="closed" value="true" />
      <created>1753626058563</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753626058563</updated>
    </task>
    <task id="LOCAL-00014" summary="重构">
      <option name="closed" value="true" />
      <created>1753626113299</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753626113299</updated>
    </task>
    <task id="LOCAL-00015" summary="重构">
      <option name="closed" value="true" />
      <created>1753708438197</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1753708438197</updated>
    </task>
    <option name="localTasksCounter" value="16" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="dev-icon" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <MESSAGE value="修复表格" />
    <MESSAGE value="重构" />
    <option name="LAST_COMMIT_MESSAGE" value="重构" />
  </component>
</project>