<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="getList"
      @refresh="refresh"
      @search="getList"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:operlog:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:operlog:remove']"
          icon="Delete"
          plain
          type="danger"
          @click="handleClean"
          >{{ $t('action.clear') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['monitor:operlog:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
    </el-row>

    <!--<el-table ref="operlogRef" v-loading="loading" :data="operlogList" @selection-change="handleSelectionChange" height="540px" :default-sort="defaultSort" @sort-change="handleSortChange">-->
    <art-table
      ref="operlogRef"
      v-loading="loading"
      :data="operlogList"
      :default-sort="defaultSort"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      row-key="operId"
      @search="getList"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <el-table-column align="center" type="selection" width="50" />
      <el-table-column :label="$t('number.log')" align="center" prop="operId" />
      <el-table-column
        :label="$t('module.system')"
        :show-overflow-tooltip="true"
        align="center"
        prop="title"
      />
      <el-table-column :label="$t('type.operation')" align="center" prop="businessType">
        <template #default="scope">
          <dict-tag :options="sys_oper_type" :value="scope.row.businessType" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operator')"
        :show-overflow-tooltip="true"
        :sort-orders="['descending', 'ascending']"
        align="center"
        prop="operName"
        sortable="custom"
        width="110"
      />
      <el-table-column
        :label="$t('address.operation')"
        :show-overflow-tooltip="true"
        align="center"
        prop="operIp"
        width="130"
      />
      <el-table-column :label="$t('status.operation')" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_common_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('date.operation')"
        :sort-orders="['descending', 'ascending']"
        align="center"
        prop="operTime"
        sortable="custom"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.operTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('time.consumption')"
        :show-overflow-tooltip="true"
        :sort-orders="['descending', 'ascending']"
        align="center"
        prop="costTime"
        sortable="custom"
        width="110"
      >
        <template #default="scope">
          <span>{{ scope.row.costTime }}{{ $t('milliseconds.cost.time') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['monitor:operlog:query']"
            icon="View"
            link
            type="primary"
            @click="handleView(scope.row, scope.index)"
            >{{ $t('details') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!-- 操作日志详细 -->
    <detail v-model:show="open" :title="title" :operlogData="currentOperlog" />
  </div>
</template>

<script name="Operlog" setup>
  import { getCurrentInstance } from 'vue'
  import { cleanOperlog, delOperlog, list } from '@/api/monitor/operlog'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import detail from './components/detail.vue'

  const { proxy } = getCurrentInstance()
  const { sys_oper_type, sys_common_status } = proxy.useDict('sys_oper_type', 'sys_common_status')

  const operlogList = ref([])
  const open = ref(false)
  const currentOperlog = ref({})
  const loading = ref(true)
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')
  const dateRange = ref([])
  const defaultSort = ref({ prop: 'operTime', order: 'descending' })

  const data = reactive({
    fromKey: 'operlog',
    fromName: 'operlog',
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      operIp: undefined,
      title: undefined,
      operName: undefined,
      businessType: undefined,
      status: undefined
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询操作日志 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    list(proxy.addDateRange(queryParams.value, dateRange.value)).then(({ data }) => {
      operlogList.value = data.records
      total.value = data.totalRow
      loading.value = false
    })
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.operId)
    multiple.value = !selection.length
  }

  /** 排序触发事件 */
  function handleSortChange(column, prop, order) {
    queryParams.value.orderByColumn = column.prop
    queryParams.value.isAsc = column.order
    getList()
  }

  /** 详细按钮操作 */
  function handleView(row) {
    currentOperlog.value = row
    title.value = proxy.$t('details.log.operation')
    open.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const operIds = row.operId || ids.value
    proxy.$modal
      .confirm(proxy.$t('confirm.delete.log.number') + operIds + proxy.$t('item.data.question'))
      .then(function () {
        return delOperlog(operIds)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('delete.success'))
      })
      .catch(() => {})
  }

  /** 清空按钮操作 */
  function handleClean() {
    proxy.$modal
      .confirm(proxy.$t('confirm.clear.operation.logs'))
      .then(function () {
        return cleanOperlog()
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('clear.success'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'monitor/operlog/export',
      {
        ...queryParams.value
      },
      `config_${new Date().getTime()}.xlsx`
    )
  }

  const refresh = () => {
    getList()
  }

  getList()
</script>
