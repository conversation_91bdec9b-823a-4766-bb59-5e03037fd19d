<!--任务管理-->
<template>
  <div class="page-content">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
      <el-form-item :label="$t('executor')" prop="jobGroup">
        <el-select
          v-model="queryParams.jobGroup"
          :placeholder="$t('executor.select')"
          filterable
          style="width: 200px"
          @change="getList"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('status')" prop="triggerStatus">
        <el-select
          v-model="queryParams.triggerStatus"
          :placeholder="$t('executor.select')"
          style="width: 200px"
        >
          <el-option
            v-for="item in task_status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('description.task')" prop="jobDesc">
        <el-input
          v-model="queryParams.jobDesc"
          :placeholder="$t('description.task.input')"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="jobHandler" prop="executorHandler">
        <el-input
          v-model="queryParams.executorHandler"
          :placeholder="$t('jobhandler.input')"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('person.charge')" prop="author">
        <el-input
          v-model="queryParams.author"
          :placeholder="$t('person.charge.input')"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">{{
          $t('action.search')
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('action.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:xxlJobInfo:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:xxlJobInfo:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <art-table
      v-loading="loading"
      :data="xxlJobInfoList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="getList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column :label="$t('id.task')" align="center" prop="jobGroup" />
      <el-table-column :label="$t('description.task')" align="center" prop="jobDesc" />
      <el-table-column :label="$t('type.scheduling')" align="center" prop="scheduleType">
        <template #default="scope">
          {{ scope.row.scheduleType + '：' + scope.row.scheduleConf }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('mode.operating')" align="center" prop="jobDesc">
        <template #default="scope">
          <span v-if="scope.row.executorHandler !== null && scope.row.executorHandler !== ''"
            >{{ scope.row.glueType + '： ' + scope.row.executorHandler }}
          </span>
          <span v-else>{{ formatterVal(xxlInfo.glueTypeEnum, scope.row.glueType) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('person.charge')" align="center" prop="author" />
      <el-table-column :label="$t('status')" align="center" prop="triggerStatus">
        <template #default="scope">
          <el-tag v-if="scope.row.triggerStatus === 1" type="success">{{
            $t('action.start')
          }}</el-tag>
          <el-tag v-else type="danger">{{ $t('action.stop') }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-dropdown @command="more">
            <el-button type="primary"
              >{{ $t('operation') }}
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{ type: 'one', id: scope.row.id }">{{
                  $t('execute.once')
                }}</el-dropdown-item>
                <el-dropdown-item
                  :command="{ type: 'log', id: scope.row.id, jobGroup: scope.row.jobGroup }"
                >
                  {{ $t('log.query') }}
                </el-dropdown-item>
                <el-dropdown-item :command="{ type: 'node', id: scope.row.id }"
                  >{{ $t('node.registered') }}
                </el-dropdown-item>
                <el-dropdown-item
                  :command="{
                    type: 'executeTime',
                    scheduleType: scope.row.scheduleType,
                    scheduleConf: scope.row.scheduleConf
                  }"
                >
                  $t('time.execution.next')
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.glueType.startsWith('GLUE')"
                  :command="{ type: 'ide', id: scope.row.id }"
                  divided
                  >GLUE IDE
                </el-dropdown-item>

                <el-dropdown-item
                  v-if="scope.row.triggerStatus == '1'"
                  :command="{ type: 'stop', id: scope.row.id }"
                  divided
                >
                  $t('action.stop')
                </el-dropdown-item>
                <el-dropdown-item v-else :command="{ type: 'run', id: scope.row.id }" divided
                  >{{ $t('action.start') }}
                </el-dropdown-item>
                <el-dropdown-item :command="{ type: 'edit', id: scope.row.id }">{{
                  $t('action.edit')
                }}</el-dropdown-item>
                <el-dropdown-item :command="{ type: 'delete', id: scope.row.id }"
                  >{{ $t('action.delete') }}
                </el-dropdown-item>
                <el-dropdown-item :command="{ type: 'copy', id: scope.row.id }">{{
                  $t('action.copy')
                }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改【请填写功能名称】对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="50%">
      <el-form ref="xxlJobInfoRef" :model="form" :rules="rules" inline>
        <h3>{{ $t('configuration.basic') }}</h3>
        <hr class="mt20 mb10" />
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('parameters.task')" prop="jobGroup" style="width: 100%">
              <el-select
                v-model="form.jobGroup"
                :placeholder="$t('executor.select')"
                style="width: 100%"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('description.task')" prop="jobDesc" style="width: 100%">
              <el-input
                v-model="form.jobDesc"
                :placeholder="$t('description.task.input')"
                clearable
                resize="none"
                type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('person.charge')" prop="author" style="width: 100%">
              <el-input v-model="form.author" :placeholder="$t('person.charge')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('email.alarm')" prop="alarmEmail" style="width: 100%">
              <el-input v-model="form.alarmEmail" :placeholder="$t('email.alarm')" type="email" />
            </el-form-item>
          </el-col>
        </el-row>
        <h3>{{ $t('configuration.scheduling') }}</h3>
        <hr class="mt20 mb10" />
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('type.scheduling')" prop="scheduleType" style="width: 100%">
              <el-select
                v-model="form.scheduleType"
                :placeholder="$t('type.scheduling.select')"
                style="width: 100%"
              >
                <el-option
                  v-for="item in xxlInfo.scheduleTypeEnum"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="form.scheduleType === 'CRON'"
              label="Cron"
              prop="scheduleConf"
              style="width: 100%"
            >
              <el-input
                v-model="form.scheduleConf"
                placeholder="* * * * * ? *"
                readonly
                @click="openCron"
              ></el-input>
            </el-form-item>
            <el-form-item
              v-else-if="form.scheduleType === 'FIX_RATE'"
              :label="$t('speed.fixed')"
              prop="scheduleConf"
              style="width: 100%"
            >
              <el-input
                v-model.number="form.scheduleConf"
                :placeholder="$t('speed.fixed')"
                type="number"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <h3>{{ $t('configuration.task') }}</h3>
        <hr class="mt20 mb10" />
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('mode.operating')" prop="glueType" style="width: 100%">
              <el-select
                v-model="form.glueType"
                :placeholder="$t('mode.operating.select')"
                style="width: 100%"
              >
                <el-option
                  v-for="item in xxlInfo.glueTypeEnum"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :prop="form.glueType === 'BEAN' ? 'executorHandler' : ''"
              label="JobHandler"
              style="width: 100%"
            >
              <el-input
                v-model="form.executorHandler"
                :disabled="form.glueType !== 'BEAN'"
                placeholder="JobHandler"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              :label="$t('parameters.runtime')"
              prop="executorParam"
              style="width: 100%"
            >
              <el-input
                v-model="form.executorParam"
                :placeholder="$t('parameters.runtime.input')"
                resize="none"
                type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <h3>{{ $t('configuration.advanced') }}</h3>
        <hr class="mt20 mb10" />
        <el-row>
          <el-col :span="12">
            <el-form-item
              :label="$t('policy.routing')"
              prop="executorRouteStrategy"
              style="width: 100%"
            >
              <el-select
                v-model="form.executorRouteStrategy"
                :placeholder="$t('strategy.routing.select')"
                style="width: 100%"
              >
                <el-option
                  v-for="item in xxlInfo.executorRouteStrategyEnum"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('id.subtask')" prop="childJobId" style="width: 100%">
              <el-input v-model="form.childJobId" :placeholder="$t('id.subtask.input')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              :label="$t('strategy.expiry.scheduling')"
              prop="misfireStrategy"
              style="width: 100%"
            >
              <el-select
                v-model="form.misfireStrategy"
                :placeholder="$t('policy.expiry.scheduling.select')"
                style="width: 100%"
              >
                <el-option
                  v-for="item in xxlInfo.misfireStrategyEnum"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('strategy.handling.blocking')"
              prop="executorBlockStrategy"
              style="width: 100%"
            >
              <el-select
                v-model="form.executorBlockStrategy"
                :placeholder="$t('strategy.blocking.handling.select')"
                style="width: 100%"
              >
                <el-option
                  v-for="item in xxlInfo.executorBlockStrategyEnum"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              :label="$t('time.timeout.task')"
              prop="executorTimeout"
              style="width: 100%"
            >
              <el-input
                v-model.number="form.executorTimeout"
                :placeholder="$t('time.timeout.task')"
                type="number"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('retries.failure')"
              prop="executorFailRetryCount"
              style="width: 100%"
            >
              <el-input
                v-model.number="form.executorFailRetryCount"
                :placeholder="$t('retries.failure')"
                type="number"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('action.confirm') }}</el-button>
          <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="one" :title="$t('execute.once')" append-to-body width="500px">
      <el-form ref="executeOne" :model="oneForm">
        <el-form-item :label="$t('parameters.task')" prop="executorParam">
          <el-input
            v-model="oneForm.executorParam"
            :placeholder="$t('parameters.task.input')"
            type="textarea"
          />
        </el-form-item>
        <el-form-item :label="$t('address.machine')" prop="addressList">
          <el-input
            v-model="oneForm.addressList"
            :placeholder="$t('address.machine.input')"
            type="textarea"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitOne">{{ $t('action.confirm') }}</el-button>
          <el-button @click="restOneForm">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
    <!--注册节点-->
    <el-dialog v-model="nodeVisible" :title="commonTitle" append-to-body width="50%">
      <el-tag v-for="item in commonList" v-if="commonList.length > 0" class="mr5 mt5 inlineBlock">{{
        item
      }}</el-tag>
      <vue3Cron
        v-if="nodeVisible && commonList.length === 0"
        :cron="form.scheduleConf"
        i18n="cn"
        max-height="230px"
        @change="changeCron"
        @close="closeCron"
      >
      </vue3Cron>
    </el-dialog>
    <el-dialog
      v-model="ideVisible"
      :show-close="false"
      append-to-body
      fullscreen
      title="WebIDE"
      width="50%"
    >
      <template #title>
        <!--<CodeEdit @close="ideVisible=false" v-if="ideVisible" @save="save" :info="glueInfo"/>-->
      </template>
    </el-dialog>

    <textarea ref="java" style="display: none">
package com.xxl.job.service.handler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;

public class DemoGlueJobHandler extends IJobHandler {

  @Override
  public void execute() throws Exception {
    XxlJobHelper.log("XXL-JOB, Hello World.");
  }

}
</textarea
    >

    <textarea ref="shell" style="display: none">
GLUE代码初始化
glueSource: #!/bin/bash
echo "xxl-job: hello shell"

echo "脚本位置：$0"
echo "任务参数：$1"
echo "分片序号 = $2"
echo "分片总数 = $3"

echo "Good bye!"
exit 0
</textarea
    >

    <textarea ref="python" style="display: none">
#!/usr/bin/python
# -*- coding: UTF-8 -*-
import time
import sys

print "xxl-job: hello python"

print "脚本位置：", sys.argv[0]
print "任务参数：", sys.argv[1]
print "分片序号：", sys.argv[2]
print "分片总数：", sys.argv[3]

print "Good bye!"
exit(0)
</textarea
    >

    <textarea ref="php" style="display: none">


    echo "xxl-job: hello php  \n";

    echo "脚本位置：$argv[0]  \n";
    echo "任务参数：$argv[1]  \n";
    echo "分片序号 = $argv[2]  \n";
    echo "分片总数 = $argv[3]  \n";

    echo "Good bye!  \n";
    exit(0);


</textarea
    >

    <textarea ref="node" style="display: none">
#!/usr/bin/env node
console.log("xxl-job: hello nodejs")

var arguments = process.argv

console.log("脚本位置: " + arguments[1])
console.log("任务参数: " + arguments[2])
console.log("分片序号: " + arguments[3])
console.log("分片总数: " + arguments[4])

console.log("Good bye!")
process.exit(0)
</textarea
    >

    <textarea ref="powershell" style="display: none">
Write-Host "xxl-job: hello powershell"

Write-Host "脚本位置: " $MyInvocation.MyCommand.Definition
Write-Host "任务参数: "
	if ($args.Count -gt 2) { $args[0..($args.Count-3)] }
Write-Host "分片序号: " $args[$args.Count-2]
Write-Host "分片总数: " $args[$args.Count-1]

Write-Host "Good bye!"
exit 0
</textarea
    >
  </div>
</template>

<script setup>
  import {
    addJob_info,
    editorCode,
    executeHandler,
    executeOnes,
    getById,
    getRegNodeList,
    listJob_info,
    nextTime,
    remove,
    saveCode,
    start,
    stop,
    updateInfo
  } from '@/api/job/jobInfo'
  import Vue3Cron from '@/components/Cron/vue3-cron'
  import { getCurrentInstance, reactive, toRefs, watchEffect } from 'vue'
  // import CodeEdit from '@/components/Code/CodeEdit'
  const { proxy } = getCurrentInstance()

  let xxlJobInfoList = ref([])
  const open = ref(false)
  const one = ref(false)
  let nodeVisible = ref(false)

  const loading = ref(true)
  const showSearch = ref(true)
  const ideVisible = ref(false)
  const ids = ref([])
  const titles = ref([])

  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')
  const commonTitle = ref('')

  const options = ref([
    {
      value: 0,
      label: proxy.$t('all')
    }
  ])

  const { task_status } = proxy.useDict('task_status')

  const data = reactive({
    glueInfo: {},
    xxlInfo: {},
    form: {
      glueType: 'BEAN',
      scheduleType: 'CRON',
      executorRouteStrategy: 'FISRT',
      misfireStrategy: 'DO_NOTHING',
      executorBlockStrategy: 'SERIAL_EXECUTION'
    },
    commonList: [], //注册节点，下次执行时间

    oneForm: {
      executorParam: '',
      addressList: '',
      id: ''
    },
    queryParams: {
      start: 1,
      length: 10,
      jobGroup: 0,
      jobDesc: null,
      author: null,

      triggerStatus: '-1'
    },

    rules: {
      jobGroup: [{ required: true, message: proxy.$t('executor.select'), trigger: 'blur' }],
      jobDesc: [{ required: true, message: proxy.$t('description.task.input'), trigger: 'blur' }],
      author: [{ required: true, message: proxy.$t('person.charge.input'), trigger: 'blur' }],
      scheduleType: [
        { required: true, message: proxy.$t('type.scheduling.select'), trigger: 'blur' }
      ],
      scheduleConf: [{ required: true, message: proxy.$t('content.fill'), trigger: 'change' }],
      executorHandler: [
        { required: true, message: proxy.$t('content.jobhandler.fill'), trigger: 'change' }
      ],
      glueType: [{ required: true, message: proxy.$t('mode.operating.select'), trigger: 'change' }]
    }
  })

  const { queryParams, form, rules, oneForm, commonList, xxlInfo, glueInfo } = toRefs(data)

  /**
   * 保存
   * @param val
   */
  function save(val) {
    proxy.$modal
      .prompt(proxy.$t('remarks.source.code'), proxy.$t('action.save'), {
        type: 'info'
      })
      .then(({ value }) => {
        if (value == null || value.length < 4) {
          proxy.$msg.error(proxy.$t('length.remarks.source.code'))
          return
        }
        val.glueRemark = value
        saveCode(val).then((response) => {
          proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
          toCoeEditor(val.id)
        })
      })
  }

  function changeCron(val) {
    if (typeof val !== 'string') return false
    form.value.scheduleConf = val
  }

  function openCron() {
    nodeVisible.value = true
    commonTitle.value = proxy.$t('expression.cron')
  }

  function closeCron() {
    nodeVisible.value = false
  }

  /**
   * 执行一次
   */
  function submitOne() {
    executeOnes(oneForm.value).then((res) => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess(proxy.$t('execution.success'))
      } else {
        proxy.$modal.msgError(res.msg)
      }

      restOneForm()
    })
  }

  /**
   * 获取注册节点
   */
  function regNodeList(id) {
    getRegNodeList({ id }).then((res) => {
      if (res.code === 200) {
        commonList.value = res.content.registryList || []
        if (commonList.value.length > 0) {
          nodeVisible.value = true
        } else {
          proxy.$modal.msgError(proxy.$t('node.registration.none'))
        }
        return
      }
      proxy.$modal.msgError(res.msg)
    })
  }

  /**
   * 重置执行一次表单
   */
  function restOneForm() {
    oneForm.value.id = ''
    oneForm.value.addressList = ''
    oneForm.value.executorParam = ''
    proxy.resetForm('executeOne')
    one.value = false
  }

  //下次执行时间
  function nextExecuteTime(data) {
    nextTime(data).then((res) => {
      if (res.code === 200) {
        commonList.value = res.content
        nodeVisible.value = true
        return
      }
      proxy.$modal.msgError(res.msg)
    })
  }

  /**
   * 操作
   */
  function more(val) {
    switch (val.type) {
      case 'one':
        //执行一次
        one.value = true
        oneForm.value.id = val.id
        break
      case 'node':
        //注册节点
        regNodeList(val.id)
        commonTitle.value = proxy.$t('node.registered')
        break
      case 'executeTime':
        //下一次执行时间
        commonTitle.value = proxy.$t('time.execution.next')
        nextExecuteTime(val)
        break
      case 'run':
        //启动
        runTask(val)
        break
      case 'stop':
        stopTask(val)
        //停止
        break
      case 'delete':
        removeTask(val.id)
        break
      case 'open':
        openIde(val.id)
        break
      case 'ide':
        toCoeEditor(val.id)
        break
      case 'edit':
        getTaskInfo(val.id)
        break
      case 'copy':
        copyTask(val.id)
        break
      case 'log':
        proxy.$router.push({
          path: './log',
          query: val
        })
        break
    }
  }

  /**
   * 根据id获取任务详情
   * @param id
   */
  function getTaskInfo(id) {
    getById(id).then((res) => {
      if (res.code === 200) {
        title.value = proxy.$t('task.edit')
        form.value = res.data
        open.value = true
        return
      }
      proxy.$modal.msgError(res.msg)
    })
  }

  function copyTask(id) {
    getById(id).then((res) => {
      if (res.code === 200) {
        title.value = proxy.$t('task.copy')
        form.value = res.data
        form.value.id = null
        open.value = true
        return
      }
      proxy.$modal.msgError(res.msg)
    })
  }

  /**
   * 代码编辑
   */
  function toCoeEditor(id) {
    editorCode({ jobId: id }).then((res) => {
      if (res.code === 200) {
        ideVisible.value = true
        glueInfo.value = res.data
        return
      }
      proxy.$modal.msgError(res.msg)
    })
  }

  /**
   * 删除任务
   */
  function removeTask(id) {
    proxy.$modal.confirm(proxy.$t('task.delete.confirm')).then(() => {
      remove({ id }).then((res) => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
          getList()
          return
        }
        proxy.$modal.msgError(res.msg)
      })
    })
  }

  /**
   * 启动任务
   * @param data
   */
  function runTask(data) {
    start(data).then((res) => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess(proxy.$t('operation.success'))
        getList()
        return
      }
      proxy.$modal.msgError(res.msg)
    })
  }

  watchEffect(() => {
    switch (form.value.glueType) {
      case 'BEAN':
        form.value.glueSource = null
        break
      case 'GLUE_GROOVY':
        form.value.glueSource = proxy.$refs.java.value
        break

      case 'GLUE_SHELL':
        form.value.glueSource = proxy.$refs.shell.value
        break
      case 'GLUE_PYTHON':
        form.value.glueSource = proxy.$refs.python.value
        break
      case 'GLUE_PHP':
        form.value.glueSource = '<?php' + proxy.$refs.php.value + '?>'
        break
      case 'GLUE_NODEJS':
        form.value.glueSource = proxy.$refs.node.value
        break
      case 'GLUE_POWERSHELL':
        form.value.glueSource = proxy.$refs.powershell.value
        break
    }
    form.value.glueRemark = proxy.$t('initialization.code.glue')
  })

  /**
   * 停止任务
   */
  function stopTask(data) {
    stop(data).then((res) => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess(proxy.$t('operation.success'))
        getList()
        return
      }
      proxy.$modal.msgError(res.msg)
    })
  }

  /**
   * 获取执行器列表
   */
  function getExecuteHandler() {
    executeHandler()
      .then((res) => {
        // 路由策略-列表
        xxlInfo.value.executorRouteStrategyEnum = res.executorRouteStrategyEnum
        //运行模式
        xxlInfo.value.glueTypeEnum = res.glueTypeEnum
        //阻塞处理策略-字典
        xxlInfo.value.executorBlockStrategyEnum = res.executorBlockStrategyEnum
        //调度类型
        xxlInfo.value.scheduleTypeEnum = res.scheduleTypeEnum
        //调度过期策略
        xxlInfo.value.misfireStrategyEnum = res.misfireStrategyEnum

        let isAdmin = localStorage.getItem('isAdmin')
        if (isAdmin === 'false') {
          options.value = res.data
          queryParams.value.jobGroup = res.data[0].value
        } else {
          options.value = options.value.concat(res.data)
          queryParams.value.jobGroup = 0
        }
        getList()
      })
      .catch((err) => {
        loading.value = false
      })
  }

  /** 查询【请填写功能名称】列表 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    listJob_info(queryParams.value).then((response) => {
      xxlJobInfoList.value = response.data
      total.value = response.recordsFiltered
    })
    loading.value = false
  }

  // 取消按钮
  function cancel() {
    open.value = false
    reset()
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      jobGroup: null,
      jobDesc: null,
      author: null,
      alarmEmail: null,
      scheduleType: 'CRON',
      scheduleConf: '',
      misfireStrategy: 'DO_NOTHING',
      executorRouteStrategy: 'FIRST',
      executorHandler: null,
      executorParam: null,
      executorBlockStrategy: 'SERIAL_EXECUTION',
      executorTimeout: null,
      executorFailRetryCount: null,
      glueType: 'BEAN',
      glueSource: null,
      glueRemark: null,
      glueUpdatetime: null,
      childJobid: null,
      triggerStatus: 0,
      triggerLastTime: null,
      triggerNextTime: null
    }
    proxy.resetForm('xxlJobInfoRef')
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.start = 1
    getList()
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef')
    queryParams.value.jobGroup = options.value[0].value
    handleQuery()
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id)
    titles.value = selection.map((item) => item.jobGroup)
    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset()
    open.value = true
    title.value = proxy.$t('task.new.add')
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['xxlJobInfoRef'].validate((valid) => {
      if (valid) {
        form.value.addTime = null
        form.value.updateTime = null
        form.value.glueUpdatetime = null
        if (form.value.id != null) {
          updateInfo(form.value).then((response) => {
            proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
            open.value = false
            getList()
          })
        } else {
          addJob_info(form.value).then((response) => {
            proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
            open.value = false
            getList()
          })
        }
      }
    })
  }

  getExecuteHandler()
</script>

<style scoped>
  :deep(.el-dialog__header) {
    padding: 0 !important;
    padding-bottom: 0 !important;
    margin-right: 0 !important;
    word-break: break-all;
  }

  :deep(.el-dropdown .el-dropdown-link) {
    color: #ffffff !important;
  }
</style>
