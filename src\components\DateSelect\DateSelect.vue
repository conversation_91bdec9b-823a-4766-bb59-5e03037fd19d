<template>
  <div>
    <span v-if="props.showTitle"> {{ type }}：</span>
    <el-date-picker
      v-model="value"
      :end-placeholder="$t('date.end')"
      :range-separator="$t('action.to')"
      :shortcuts="shortcuts"
      :start-placeholder="$t('date.start')"
      type="datetimerange"
      value-format="YYYY-MM-DD HH:mm:ss"
      @change="handleChange"
    />
  </div>
</template>

<script setup>
  import { getCurrentInstance, ref } from 'vue'

  const { proxy } = getCurrentInstance()

  let emits = defineEmits(['change'])
  let props = defineProps({
    showTitle: {
      type: Boolean,
      default: false
    }
  })

  let type = ref(proxy.$t('date.last.week'))

  let value = ref([])

  const shortcuts = [
    {
      text: proxy.$t('date.today'),
      value: () => {
        let start = new Date(new Date().setHours(0, 0, 0, 0))
        let end = new Date(new Date().setHours(23, 59, 59, 0))
        type.value = proxy.$t('date.today')
        return [start, end]
      }
    },
    {
      text: proxy.$t('date.yesterday'),
      value: () => {
        let toData = new Date(new Date().toLocaleDateString()).getTime()
        let yesterdayStart = toData - 3600 * 24 * 1000
        let yesterdayEnd = yesterdayStart + 24 * 60 * 60 * 1000 - 1
        type.value = proxy.$t('date.yesterday')
        return [yesterdayStart, yesterdayEnd]
      }
    },
    {
      text: proxy.$t('date.last.week'),
      value: () => {
        type.value = proxy.$t('date.last.week')
        return [getBeginLastWeek(), getEndLastWeek()]
      }
    },
    {
      text: proxy.$t('date.last.week'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setDate(start.getDate() - 7)
        start.setHours(0, 0, 0, 0)
        end.setHours(23, 59, 59, 0)
        type.value = proxy.$t('date.last.week')
        return [start, end]
      }
    },
    {
      text: proxy.$t('date.this.month'),
      value: () => {
        type.value = proxy.$t('date.this.month')
        return [getBeginMonth(), getEndMonth()]
      }
    },
    {
      text: proxy.$t('date.last.month'),
      value: () => {
        type.value = proxy.$t('date.last.month')
        return [getBeginLastMonth(), getEndLastMonth()]
      }
    },
    {
      text: proxy.$t('months.last.3'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setDate(start.getDate() - 90)
        start.setHours(0, 0, 0, 0)
        end.setHours(23, 59, 59, 0)
        type.value = proxy.$t('months.last.3')
        return [start, end]
      }
    }
  ]

  /***
   * 上月的第一天时间
   */
  function getBeginLastMonth() {
    //获取当前时间
    let currentDate = getCurrentDate()
    //获得当前月份0-11
    let currentMonth = currentDate.getMonth()
    //获得当前年份4位年
    let currentYear = currentDate.getFullYear()
    //获得上一个月的第一天
    let priorMonthFirstDay = getPriorMonthFirstDay(currentYear, currentMonth)
    return priorMonthFirstDay
  }

  /**
   * 返回上一个月的第一天Date类型
   * @param year 年
   * @param month 月
   **/
  function getPriorMonthFirstDay(year, month) {
    //年份为0代表,是本年的第一月,所以不能减
    if (month == 0) {
      month = 11 //月份为上年的最后月份
      year-- //年份减1
      return new Date(year, month, 1)
    }
    //否则,只减去月份
    month--
    return new Date(year, month, 1)
  }

  /***
   * 上月的最后一天时间
   */
  function getEndLastMonth() {
    //获取当前时间
    let currentDate = getCurrentDate()
    //获得当前月份0-11
    let currentMonth = currentDate.getMonth()
    //获得当前年份4位年
    let currentYear = currentDate.getFullYear()

    //当为12月的时候年份需要加1
    //月份需要更新为0 也就是下一年的第一个月
    if (currentMonth == 11) {
      currentYear++
      currentMonth = 0 //就为
    } else {
      //否则只是月份增加,以便求的下一月的第一天
      currentMonth++
    }

    //一天的毫秒数
    let millisecond = 1000 * 60 * 60 * 24
    //求出上月的最后一天
    let lastDay = new Date(getBeginMonth().getTime() - millisecond)

    return endTime(lastDay)
  }

  /***
   * 上周的开始
   */
  function getBeginLastWeek() {
    let currentDate = getCurrentDate()
    let first = currentDate.getDate() - currentDate.getDay() - 6
    let startDate = new Date(currentDate.setDate(first))
    return startTime(startDate)
  }

  function startTime(time) {
    const nowTimeDate = new Date(time)
    return nowTimeDate.setHours(0, 0, 0, 0)
  }

  function endTime(time) {
    const nowTimeDate = new Date(time)
    return nowTimeDate.setHours(23, 59, 59, 999)
  }

  /***
   * 上周的结束
   */
  function getEndLastWeek() {
    let currentDate = getCurrentDate()
    let first = currentDate.getDate() - currentDate.getDay() - 6
    let last = first + 6
    let endDate = new Date(currentDate.setDate(last))
    return endTime(endDate)
  }

  /***
   * 当前时间
   */
  function getCurrentDate() {
    return new Date()
  }

  /***
   * 本月的第一天时间
   */
  function getBeginMonth() {
    let currentDate = getCurrentDate()
    let currentMonth = currentDate.getMonth()
    //获得当前年份4位年
    let currentYear = currentDate.getFullYear()
    //求出本月第一天
    let firstDay = new Date(currentYear, currentMonth, 1)

    return firstDay
  }

  /***
   * 本月的最后一天时间
   */
  function getEndMonth() {
    //获取当前时间
    let currentDate = getCurrentDate()
    let fullYear = currentDate.getFullYear()
    let month = currentDate.getMonth() + 1 // getMonth 方法返回 0-11，代表1-12月
    let endOfMonth = new Date(fullYear, month, 0)
    return endTime(endOfMonth)
  }

  function handleChange(val) {
    if (!val) {
      type.value = proxy.$t('date.last.week')
      emits('change')
      return
    }
    emits('change', {
      startDate: val[0],
      endDate: val[1]
    })
  }

  /**
   *清除时间
   */
  function clear() {
    type.value = proxy.$t('date.last.week')
    value.value = []
    emits('change')
  }

  defineExpose({ clear })
</script>

<style scoped></style>
