<template>
  <Drawer v-model="open" @open="init" :size="800">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form :model="form" label-width="120px">
          <el-row :gutter="0">
            <!-- 操作模块 -->
            <el-col :span="12">
              <el-form-item :label="$t('module.operation')">
                <span>{{ form.title }} / {{ typeFormat(form) }}</span>
              </el-form-item>
            </el-col>
            <!-- 登录信息 -->
            <el-col :span="12">
              <el-form-item :label="$t('information.login')">
                <span>{{ form.operName }} / {{ form.operIp }} / {{ form.operLocation }}</span>
              </el-form-item>
            </el-col>
            <!-- 请求地址 -->
            <el-col :span="12">
              <el-form-item :label="$t('address.request')">
                <span>{{ form.operUrl }}</span>
              </el-form-item>
            </el-col>
            <!-- 请求方式 -->
            <el-col :span="12">
              <el-form-item :label="$t('method.request')">
                <span>{{ form.requestMethod }}</span>
              </el-form-item>
            </el-col>
            <!-- 操作状态 -->
            <el-col :span="8">
              <el-form-item :label="$t('status.operation')">
                <dict-tag :options="sys_common_status" :value="form.status" />
              </el-form-item>
            </el-col>
            <!-- 消耗时间 -->
            <el-col :span="8">
              <el-form-item :label="$t('time.consumption')">
                <span>{{ form.costTime }}{{ $t('milliseconds.cost.time') }}</span>
              </el-form-item>
            </el-col>
            <!-- 操作时间 -->
            <el-col :span="8">
              <el-form-item :label="$t('time.operation')">
                <span>{{ parseTime(form.operTime) }}</span>
              </el-form-item>
            </el-col>
            <!-- 操作方法 -->
            <el-col :span="24">
              <el-form-item :label="$t('method.operation')">
                <span>{{ form.method }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>

      <!-- 请求参数 -->
      <DrawerTitle>
        <template #default>
          {{ $t('parameters.request') }}
          <el-button
            v-if="form.operParam && form.operParam !== '-'"
            v-copyText="form.operParam"
            type="primary"
            size="small"
            :icon="DocumentCopy"
            style="margin-left: 10px"
            circle
          />
        </template>
      </DrawerTitle>
      <DrawerGroup>
        <div class="param-content">
          <pre>{{ form.operParam || '-' }}</pre>
        </div>
      </DrawerGroup>

      <!-- 返回参数 -->
      <DrawerTitle>
        <template #default>
          {{ $t('parameters.return') }}
          <el-button
            v-if="form.jsonResult && form.jsonResult !== '-'"
            v-copyText="form.jsonResult"
            type="primary"
            size="small"
            :icon="DocumentCopy"
            style="margin-left: 10px"
            circle
          />
        </template>
      </DrawerTitle>
      <DrawerGroup>
        <div class="param-content">
          <pre>{{ form.jsonResult || '-' }}</pre>
        </div>
      </DrawerGroup>

      <!-- 异常信息 -->
      <DrawerTitle v-if="form.status === 1">
        <template #default>
          {{ $t('information.exception') }}
          <el-button
            v-if="form.errorMsg && form.errorMsg !== '-'"
            v-copyText="form.errorMsg"
            type="primary"
            size="small"
            :icon="DocumentCopy"
            style="margin-left: 10px"
            circle
          />
        </template>
      </DrawerTitle>
      <DrawerGroup v-if="form.status === 1">
        <div class="error-content">
          <pre>{{ form.errorMsg || '-' }}</pre>
        </div>
      </DrawerGroup>

      <!-- CURL -->
      <DrawerTitle>
        <template #default>
          CURL
          <el-button
            v-if="form.curl && form.curl.trim()"
            v-copyText="form.curl"
            type="primary"
            size="small"
            :icon="DocumentCopy"
            style="margin-left: 10px"
            circle
          />
        </template>
      </DrawerTitle>
      <DrawerGroup>
        <el-input v-model="form.curl" :rows="5" readonly type="textarea" placeholder="CURL命令" />
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs } from 'vue'
  import { DocumentCopy } from '@element-plus/icons-vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'

  const { proxy } = getCurrentInstance()
  const { sys_oper_type, sys_common_status } = proxy.useDict('sys_oper_type', 'sys_common_status')

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    operlogData: {
      type: Object,
      default: () => ({})
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  /** 操作日志类型字典翻译 */
  function typeFormat(row) {
    return proxy.selectDictLabel(sys_oper_type.value, row.businessType)
  }

  const init = () => {
    reset()
    if (props.operlogData && Object.keys(props.operlogData).length > 0) {
      data.form = { ...props.operlogData }
    }
  }

  // 表单重置
  function reset() {
    form.value = {}
  }
</script>

<style scoped>
  .param-content {
    padding: 16px;
    border: 1px solid var(--art-card-border);
    border-radius: 4px;
    min-height: 100px;
    max-height: 300px;
    overflow-y: auto;
  }

  .param-content pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
  }

  .error-content {
    padding: 16px;
    border: 1px solid rgba(var(--art-error), 0.3);
    border-radius: 4px;
    min-height: 100px;
    max-height: 300px;
    overflow-y: auto;
  }

  .error-content pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    color: rgb(var(--art-error));
  }
</style>
